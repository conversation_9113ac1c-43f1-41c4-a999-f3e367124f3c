#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة الشحنات المتكامل
ShipmentPro - Integrated Shipment Management System

المطور: نظام إدارة الشحنات المتقدم
التاريخ: 2025
الإصدار: 1.0.0
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTranslator, QLocale, Qt
from PySide6.QtGui import QFont, QIcon

from src.ui.main_window import MainWindow
from src.database.database_manager import DatabaseManager
from src.utils.config import Config


def setup_application():
    """إعداد التطبيق الأساسي"""
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية والاتجاه RTL
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont("Segoe UI", 10)
    font.setFamily("Tahoma")  # خط يدعم العربية بشكل جيد
    app.setFont(font)
    
    # إعداد أيقونة التطبيق
    app.setWindowIcon(QIcon("assets/icons/app_icon.png"))
    
    # إعداد معلومات التطبيق
    app.setApplicationName("ShipmentPro")
    app.setApplicationDisplayName("نظام إدارة الشحنات")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("ShipmentPro Solutions")
    
    return app


def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        # إنشاء التطبيق
        app = setup_application()
        
        # تهيئة قاعدة البيانات
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # تحميل الإعدادات
        config = Config()
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.showMaximized()  # فتح في وضع ملء الشاشة
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except Exception as e:
        import traceback
        print(f"خطأ في تشغيل البرنامج: {str(e)}")
        print("تفاصيل الخطأ:")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
