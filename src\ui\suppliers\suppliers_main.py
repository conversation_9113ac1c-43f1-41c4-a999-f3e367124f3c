# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية لنظام إدارة الموردين
Main Suppliers Management Interface
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                               QPushButton, QLabel, QFrame, QMessageBox, QFormLayout,
                               QLineEdit, QTextEdit, QComboBox, QCheckBox, QSpinBox,
                               QDoubleSpinBox, QGroupBox, QGridLayout, QTableWidget,
                               QTableWidgetItem, QHeaderView, QScrollArea, QDateEdit,
                               QSplitter, QFileDialog)
from PySide6.QtCore import Qt, QDate

from ...utils.arabic_support import ArabicSupport
from ...utils.styles import AppStyles


class SuppliersMainWidget(QWidget):
    """الواجهة الرئيسية لنظام إدارة الموردين"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setLayoutDirection(Qt.RightToLeft)
        self.tab_widget.setStyleSheet(AppStyles.get_tab_style())
        
        # إضافة التبويبات
        self.add_tabs()
        
        main_layout.addWidget(self.tab_widget)
        
        # إضافة أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def add_tabs(self):
        """إضافة التبويبات"""
        # تبويب بيانات الموردين
        suppliers_tab = self.create_suppliers_tab()
        self.tab_widget.addTab(suppliers_tab, "بيانات الموردين")
        
        # تبويب حركة الموردين
        transactions_tab = self.create_transactions_tab()
        self.tab_widget.addTab(transactions_tab, "حركة الموردين")
        
        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        self.tab_widget.addTab(reports_tab, "التقارير")
    
    def create_suppliers_tab(self):
        """إنشاء تبويب بيانات الموردين"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # عنوان التبويب
        title = QLabel("إدارة بيانات الموردين")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # تبويبات فرعية لبيانات الموردين
        suppliers_sub_tabs = QTabWidget()
        suppliers_sub_tabs.setLayoutDirection(Qt.RightToLeft)
        suppliers_sub_tabs.setStyleSheet(AppStyles.get_tab_style())

        # تبويب البيانات الأساسية
        basic_data_tab = self.create_basic_data_tab()
        suppliers_sub_tabs.addTab(basic_data_tab, "البيانات الأساسية")

        # تبويب بيانات الاتصال
        contact_tab = self.create_contact_tab()
        suppliers_sub_tabs.addTab(contact_tab, "بيانات الاتصال")

        # تبويب البيانات المالية والتجارية
        financial_tab = self.create_financial_tab()
        suppliers_sub_tabs.addTab(financial_tab, "البيانات المالية")

        # تبويب المستندات
        documents_tab = self.create_documents_tab()
        suppliers_sub_tabs.addTab(documents_tab, "المستندات")

        layout.addWidget(suppliers_sub_tabs)

        return widget

    def create_basic_data_tab(self):
        """إنشاء تبويب البيانات الأساسية للموردين"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # مجموعة البيانات الأساسية
        basic_info_group = QGroupBox("البيانات الأساسية")
        basic_info_group.setStyleSheet(AppStyles.get_frame_style())
        basic_layout = QFormLayout(basic_info_group)

        self.supplier_code = QLineEdit()
        self.supplier_code.setPlaceholderText("رمز المورد")
        basic_layout.addRow("رمز المورد:", self.supplier_code)

        self.supplier_name = QLineEdit()
        self.supplier_name.setPlaceholderText("اسم المورد")
        basic_layout.addRow("اسم المورد:", self.supplier_name)

        self.supplier_name_en = QLineEdit()
        self.supplier_name_en.setPlaceholderText("Supplier Name in English")
        basic_layout.addRow("الاسم بالإنجليزية:", self.supplier_name_en)

        self.supplier_type = QComboBox()
        self.supplier_type.addItems(["محلي", "دولي", "حكومي", "خاص", "مؤسسة", "فرد"])
        basic_layout.addRow("نوع المورد:", self.supplier_type)

        self.supplier_category = QComboBox()
        self.supplier_category.addItems(["مورد رئيسي", "مورد ثانوي", "مورد طوارئ", "مورد موسمي"])
        basic_layout.addRow("فئة المورد:", self.supplier_category)

        scroll_layout.addWidget(basic_info_group)

        # مجموعة التقييم والحالة
        status_group = QGroupBox("التقييم والحالة")
        status_group.setStyleSheet(AppStyles.get_frame_style())
        status_layout = QFormLayout(status_group)

        self.supplier_rating = QSpinBox()
        self.supplier_rating.setRange(1, 5)
        self.supplier_rating.setValue(3)
        self.supplier_rating.setSuffix(" نجوم")
        status_layout.addRow("تقييم المورد:", self.supplier_rating)

        self.is_active = QCheckBox("مورد نشط")
        self.is_active.setChecked(True)
        status_layout.addRow("حالة المورد:", self.is_active)

        self.is_approved = QCheckBox("مورد معتمد")
        status_layout.addRow("الاعتماد:", self.is_approved)

        self.approval_date = QDateEdit()
        self.approval_date.setDate(QDate.currentDate())
        self.approval_date.setCalendarPopup(True)
        status_layout.addRow("تاريخ الاعتماد:", self.approval_date)

        scroll_layout.addWidget(status_group)

        # مجموعة الملاحظات
        notes_group = QGroupBox("الملاحظات")
        notes_group.setStyleSheet(AppStyles.get_frame_style())
        notes_layout = QFormLayout(notes_group)

        self.supplier_notes = QTextEdit()
        self.supplier_notes.setMaximumHeight(80)
        self.supplier_notes.setPlaceholderText("ملاحظات عامة عن المورد")
        notes_layout.addRow("الملاحظات:", self.supplier_notes)

        self.internal_notes = QTextEdit()
        self.internal_notes.setMaximumHeight(80)
        self.internal_notes.setPlaceholderText("ملاحظات داخلية (لا تظهر للمورد)")
        notes_layout.addRow("ملاحظات داخلية:", self.internal_notes)

        scroll_layout.addWidget(notes_group)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        return widget

    def create_contact_tab(self):
        """إنشاء تبويب بيانات الاتصال"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة بيانات الاتصال الرئيسية
        main_contact_group = QGroupBox("بيانات الاتصال الرئيسية")
        main_contact_group.setStyleSheet(AppStyles.get_frame_style())
        main_contact_layout = QFormLayout(main_contact_group)

        self.contact_person = QLineEdit()
        self.contact_person.setPlaceholderText("اسم الشخص المسؤول")
        main_contact_layout.addRow("الشخص المسؤول:", self.contact_person)

        self.phone = QLineEdit()
        self.phone.setPlaceholderText("رقم الهاتف")
        main_contact_layout.addRow("الهاتف:", self.phone)

        self.mobile = QLineEdit()
        self.mobile.setPlaceholderText("رقم الجوال")
        main_contact_layout.addRow("الجوال:", self.mobile)

        self.fax = QLineEdit()
        self.fax.setPlaceholderText("رقم الفاكس")
        main_contact_layout.addRow("الفاكس:", self.fax)

        self.email = QLineEdit()
        self.email.setPlaceholderText("البريد الإلكتروني")
        main_contact_layout.addRow("البريد الإلكتروني:", self.email)

        self.website = QLineEdit()
        self.website.setPlaceholderText("الموقع الإلكتروني")
        main_contact_layout.addRow("الموقع الإلكتروني:", self.website)

        layout.addWidget(main_contact_group)

        # مجموعة العنوان
        address_group = QGroupBox("بيانات العنوان")
        address_group.setStyleSheet(AppStyles.get_frame_style())
        address_layout = QFormLayout(address_group)

        self.address = QTextEdit()
        self.address.setMaximumHeight(80)
        self.address.setPlaceholderText("العنوان التفصيلي")
        address_layout.addRow("العنوان:", self.address)

        self.city = QLineEdit()
        self.city.setPlaceholderText("المدينة")
        address_layout.addRow("المدينة:", self.city)

        self.state = QLineEdit()
        self.state.setPlaceholderText("المنطقة/الولاية")
        address_layout.addRow("المنطقة:", self.state)

        self.country = QComboBox()
        self.country.addItems(["السعودية", "الإمارات", "الكويت", "قطر", "البحرين", "عمان", "مصر", "الأردن"])
        address_layout.addRow("الدولة:", self.country)

        self.postal_code = QLineEdit()
        self.postal_code.setPlaceholderText("الرمز البريدي")
        address_layout.addRow("الرمز البريدي:", self.postal_code)

        layout.addWidget(address_group)

        # مجموعة جهات الاتصال الإضافية
        additional_contacts_group = QGroupBox("جهات الاتصال الإضافية")
        additional_contacts_group.setStyleSheet(AppStyles.get_frame_style())
        additional_contacts_layout = QVBoxLayout(additional_contacts_group)

        self.contacts_table = QTableWidget()
        self.contacts_table.setColumnCount(6)
        self.contacts_table.setHorizontalHeaderLabels([
            "الاسم", "المنصب", "القسم", "الهاتف", "الجوال", "البريد الإلكتروني"
        ])
        self.contacts_table.setMaximumHeight(150)

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.contacts_table)

        # إضافة بيانات تجريبية
        self.contacts_table.setRowCount(2)
        contacts_data = [
            ["أحمد محمد", "مدير المبيعات", "المبيعات", "011-4567890", "0501234567", "<EMAIL>"],
            ["فاطمة علي", "مسؤولة الحسابات", "المالية", "011-4567891", "0507654321", "<EMAIL>"]
        ]

        for row, data in enumerate(contacts_data):
            for col, value in enumerate(data):
                self.contacts_table.setItem(row, col, QTableWidgetItem(value))

        additional_contacts_layout.addWidget(self.contacts_table)

        # أزرار إدارة جهات الاتصال
        contacts_buttons_layout = QHBoxLayout()

        add_contact_btn = QPushButton("إضافة جهة اتصال")
        add_contact_btn.setStyleSheet(AppStyles.get_success_button_style())
        contacts_buttons_layout.addWidget(add_contact_btn)

        edit_contact_btn = QPushButton("تعديل")
        edit_contact_btn.setStyleSheet(AppStyles.get_button_style())
        contacts_buttons_layout.addWidget(edit_contact_btn)

        delete_contact_btn = QPushButton("حذف")
        delete_contact_btn.setStyleSheet(AppStyles.get_danger_button_style())
        contacts_buttons_layout.addWidget(delete_contact_btn)

        contacts_buttons_layout.addStretch()
        additional_contacts_layout.addLayout(contacts_buttons_layout)

        layout.addWidget(additional_contacts_group)

        return widget

    def create_financial_tab(self):
        """إنشاء تبويب البيانات المالية والتجارية"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة البيانات التجارية
        commercial_group = QGroupBox("البيانات التجارية")
        commercial_group.setStyleSheet(AppStyles.get_frame_style())
        commercial_layout = QFormLayout(commercial_group)

        self.commercial_register = QLineEdit()
        self.commercial_register.setPlaceholderText("رقم السجل التجاري")
        commercial_layout.addRow("السجل التجاري:", self.commercial_register)

        self.tax_number = QLineEdit()
        self.tax_number.setPlaceholderText("الرقم الضريبي")
        commercial_layout.addRow("الرقم الضريبي:", self.tax_number)

        self.license_number = QLineEdit()
        self.license_number.setPlaceholderText("رقم الترخيص")
        commercial_layout.addRow("رقم الترخيص:", self.license_number)

        layout.addWidget(commercial_group)

        # مجموعة البيانات المالية
        financial_group = QGroupBox("البيانات المالية")
        financial_group.setStyleSheet(AppStyles.get_frame_style())
        financial_layout = QFormLayout(financial_group)

        self.credit_limit = QDoubleSpinBox()
        self.credit_limit.setRange(0.00, 9999999.99)
        self.credit_limit.setDecimals(2)
        self.credit_limit.setSuffix(" ريال")
        financial_layout.addRow("حد الائتمان:", self.credit_limit)

        self.payment_terms = QComboBox()
        self.payment_terms.addItems([
            "نقداً عند التسليم", "30 يوم", "60 يوم", "90 يوم",
            "تحويل فوري", "شيك مؤجل", "اعتماد مستندي"
        ])
        financial_layout.addRow("شروط الدفع:", self.payment_terms)

        self.currency = QComboBox()
        self.currency.addItems(["ريال سعودي", "دولار أمريكي", "يورو", "درهم إماراتي"])
        financial_layout.addRow("العملة:", self.currency)

        layout.addWidget(financial_group)

        # مجموعة الحساب البنكي
        bank_group = QGroupBox("بيانات الحساب البنكي")
        bank_group.setStyleSheet(AppStyles.get_frame_style())
        bank_layout = QFormLayout(bank_group)

        self.bank_name = QLineEdit()
        self.bank_name.setPlaceholderText("اسم البنك")
        bank_layout.addRow("اسم البنك:", self.bank_name)

        self.account_number = QLineEdit()
        self.account_number.setPlaceholderText("رقم الحساب")
        bank_layout.addRow("رقم الحساب:", self.account_number)

        self.iban = QLineEdit()
        self.iban.setPlaceholderText("رقم الآيبان")
        bank_layout.addRow("رقم الآيبان:", self.iban)

        self.swift_code = QLineEdit()
        self.swift_code.setPlaceholderText("رمز السويفت")
        bank_layout.addRow("رمز السويفت:", self.swift_code)

        layout.addWidget(bank_group)

        # مجموعة الإحصائيات المالية
        stats_group = QGroupBox("الإحصائيات المالية")
        stats_group.setStyleSheet(AppStyles.get_frame_style())
        stats_layout = QFormLayout(stats_group)

        total_purchases_label = QLabel("0.00 ريال")
        total_purchases_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        stats_layout.addRow("إجمالي المشتريات:", total_purchases_label)

        outstanding_balance_label = QLabel("0.00 ريال")
        outstanding_balance_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        stats_layout.addRow("الرصيد المستحق:", outstanding_balance_label)

        last_transaction_label = QLabel("لا توجد معاملات")
        last_transaction_label.setStyleSheet("color: #7f8c8d;")
        stats_layout.addRow("آخر معاملة:", last_transaction_label)

        layout.addWidget(stats_group)

        return widget

    def create_documents_tab(self):
        """إنشاء تبويب المستندات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة رفع المستندات
        upload_group = QGroupBox("رفع مستند جديد")
        upload_group.setStyleSheet(AppStyles.get_frame_style())
        upload_layout = QFormLayout(upload_group)

        self.document_type = QComboBox()
        self.document_type.addItems([
            "السجل التجاري", "الرقم الضريبي", "رخصة البلدية",
            "شهادة الجودة", "عقد التوريد", "مستند أخر"
        ])
        upload_layout.addRow("نوع المستند:", self.document_type)

        file_layout = QHBoxLayout()
        self.file_path = QLineEdit()
        self.file_path.setPlaceholderText("اختر ملف...")
        file_layout.addWidget(self.file_path)

        browse_btn = QPushButton("استعراض")
        browse_btn.setStyleSheet(AppStyles.get_button_style())
        browse_btn.clicked.connect(self.browse_file)
        file_layout.addWidget(browse_btn)

        upload_layout.addRow("الملف:", file_layout)

        self.document_description = QLineEdit()
        self.document_description.setPlaceholderText("وصف المستند")
        upload_layout.addRow("الوصف:", self.document_description)

        upload_btn = QPushButton("رفع المستند")
        upload_btn.setStyleSheet(AppStyles.get_success_button_style())
        upload_layout.addRow(upload_btn)

        layout.addWidget(upload_group)

        # مجموعة المستندات المرفوعة
        documents_group = QGroupBox("المستندات المرفوعة")
        documents_group.setStyleSheet(AppStyles.get_frame_style())
        documents_layout = QVBoxLayout(documents_group)

        self.documents_table = QTableWidget()
        self.documents_table.setColumnCount(5)
        self.documents_table.setHorizontalHeaderLabels([
            "نوع المستند", "اسم الملف", "الوصف", "تاريخ الرفع", "الحجم"
        ])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.documents_table)

        # إضافة بيانات تجريبية
        self.documents_table.setRowCount(3)
        documents_data = [
            ["السجل التجاري", "commercial_register.pdf", "السجل التجاري للمورد", "2024-01-15", "2.5 MB"],
            ["الرقم الضريبي", "tax_certificate.pdf", "شهادة التسجيل الضريبي", "2024-01-20", "1.8 MB"],
            ["شهادة الجودة", "quality_cert.pdf", "شهادة الأيزو 9001", "2024-02-01", "3.2 MB"]
        ]

        for row, data in enumerate(documents_data):
            for col, value in enumerate(data):
                self.documents_table.setItem(row, col, QTableWidgetItem(value))

        documents_layout.addWidget(self.documents_table)

        # أزرار إدارة المستندات
        docs_buttons_layout = QHBoxLayout()

        view_doc_btn = QPushButton("عرض المستند")
        view_doc_btn.setStyleSheet(AppStyles.get_button_style())
        docs_buttons_layout.addWidget(view_doc_btn)

        download_doc_btn = QPushButton("تحميل")
        download_doc_btn.setStyleSheet(AppStyles.get_button_style())
        docs_buttons_layout.addWidget(download_doc_btn)

        delete_doc_btn = QPushButton("حذف المستند")
        delete_doc_btn.setStyleSheet(AppStyles.get_danger_button_style())
        docs_buttons_layout.addWidget(delete_doc_btn)

        docs_buttons_layout.addStretch()
        documents_layout.addLayout(docs_buttons_layout)

        layout.addWidget(documents_group)

        return widget

    def browse_file(self):
        """استعراض ملف"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف",
            "",
            "جميع الملفات (*);;ملفات PDF (*.pdf);;ملفات الصور (*.jpg *.png *.gif)"
        )
        if file_path:
            self.file_path.setText(file_path)
    
    def create_transactions_tab(self):
        """إنشاء تبويب حركة الموردين"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        title = QLabel("إدارة حركة الموردين")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # تبويبات فرعية لحركة الموردين
        transactions_sub_tabs = QTabWidget()
        transactions_sub_tabs.setLayoutDirection(Qt.RightToLeft)
        transactions_sub_tabs.setStyleSheet(AppStyles.get_tab_style())
        
        # تبويب المعاملات المالية
        financial_tab = self.create_financial_transactions_tab()
        transactions_sub_tabs.addTab(financial_tab, "المعاملات المالية")

        # تبويب كشف الحساب
        account_tab = self.create_account_statement_tab()
        transactions_sub_tabs.addTab(account_tab, "كشف الحساب")
        
        layout.addWidget(transactions_sub_tabs)
        
        return widget

    def create_financial_transactions_tab(self):
        """إنشاء تبويب المعاملات المالية"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة فلترة المعاملات
        filter_group = QGroupBox("فلترة المعاملات")
        filter_group.setStyleSheet(AppStyles.get_frame_style())
        filter_layout = QGridLayout(filter_group)

        filter_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.trans_from_date = QDateEdit()
        self.trans_from_date.setDate(QDate.currentDate().addDays(-30))
        self.trans_from_date.setCalendarPopup(True)
        filter_layout.addWidget(self.trans_from_date, 0, 1)

        filter_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.trans_to_date = QDateEdit()
        self.trans_to_date.setDate(QDate.currentDate())
        self.trans_to_date.setCalendarPopup(True)
        filter_layout.addWidget(self.trans_to_date, 0, 3)

        filter_layout.addWidget(QLabel("نوع المعاملة:"), 1, 0)
        self.transaction_type_filter = QComboBox()
        self.transaction_type_filter.addItems(["جميع المعاملات", "فاتورة شراء", "دفعة", "خصم", "إرجاع"])
        filter_layout.addWidget(self.transaction_type_filter, 1, 1)

        filter_layout.addWidget(QLabel("المورد:"), 1, 2)
        self.trans_supplier_filter = QComboBox()
        self.trans_supplier_filter.addItems(["جميع الموردين", "شركة التقنية المتقدمة", "مؤسسة الجودة التجارية"])
        filter_layout.addWidget(self.trans_supplier_filter, 1, 3)

        apply_filter_btn = QPushButton("تطبيق الفلتر")
        apply_filter_btn.setStyleSheet(AppStyles.get_button_style())
        filter_layout.addWidget(apply_filter_btn, 2, 0, 1, 2)

        clear_trans_filter_btn = QPushButton("مسح الفلتر")
        clear_trans_filter_btn.setStyleSheet(AppStyles.get_warning_button_style())
        filter_layout.addWidget(clear_trans_filter_btn, 2, 2, 1, 2)

        layout.addWidget(filter_group)

        # جدول المعاملات المالية
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(9)
        self.transactions_table.setHorizontalHeaderLabels([
            "رقم المعاملة", "التاريخ", "المورد", "نوع المعاملة",
            "المبلغ", "العملة", "الحالة", "طريقة الدفع", "ملاحظات"
        ])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.transactions_table)

        # إضافة بيانات تجريبية
        self.transactions_table.setRowCount(6)
        transactions_data = [
            ["TXN-001", "2024-01-15", "شركة التقنية المتقدمة", "فاتورة شراء", "15000.00", "ريال", "مكتملة", "تحويل بنكي", "فاتورة أجهزة"],
            ["TXN-002", "2024-01-16", "شركة التقنية المتقدمة", "دفعة", "-15000.00", "ريال", "مكتملة", "تحويل بنكي", "دفع فاتورة TXN-001"],
            ["TXN-003", "2024-01-20", "مؤسسة الجودة التجارية", "فاتورة شراء", "8500.00", "ريال", "جزئية", "شيك", "مواد مكتبية"],
            ["TXN-004", "2024-01-22", "مؤسسة الجودة التجارية", "دفعة", "-5000.00", "ريال", "مكتملة", "نقد", "دفعة جزئية"],
            ["TXN-005", "2024-01-25", "شركة التقنية المتقدمة", "فاتورة شراء", "22000.00", "ريال", "معلقة", "اعتماد", "خوادم"],
            ["TXN-006", "2024-02-01", "مؤسسة الجودة التجارية", "إرجاع", "-500.00", "ريال", "مكتملة", "خصم", "إرجاع مواد معيبة"]
        ]

        for row, data in enumerate(transactions_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 6:  # عمود الحالة
                    if value == "مكتملة":
                        item.setBackground(Qt.green)
                    elif value == "معلقة":
                        item.setBackground(Qt.red)
                    elif value == "جزئية":
                        item.setBackground(Qt.yellow)
                elif col == 4 and value.startswith("-"):  # المبالغ السالبة
                    item.setForeground(Qt.red)
                elif col == 4 and not value.startswith("-"):  # المبالغ الموجبة
                    item.setForeground(Qt.darkGreen)
                self.transactions_table.setItem(row, col, item)

        layout.addWidget(self.transactions_table)

        # إحصائيات المعاملات
        trans_stats_layout = QHBoxLayout()

        total_invoices = QLabel("إجمالي الفواتير: 45,500.00 ريال")
        total_invoices.setStyleSheet("font-weight: bold; color: #e74c3c;")
        trans_stats_layout.addWidget(total_invoices)

        total_payments = QLabel("إجمالي المدفوعات: 20,500.00 ريال")
        total_payments.setStyleSheet("font-weight: bold; color: #27ae60;")
        trans_stats_layout.addWidget(total_payments)

        net_balance = QLabel("الرصيد الصافي: 25,000.00 ريال")
        net_balance.setStyleSheet("font-weight: bold; color: #2c3e50;")
        trans_stats_layout.addWidget(net_balance)

        trans_stats_layout.addStretch()
        layout.addLayout(trans_stats_layout)

        return widget

    def create_account_statement_tab(self):
        """إنشاء تبويب كشف الحساب"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة إعدادات كشف الحساب
        statement_settings_group = QGroupBox("إعدادات كشف الحساب")
        statement_settings_group.setStyleSheet(AppStyles.get_frame_style())
        settings_layout = QGridLayout(statement_settings_group)

        settings_layout.addWidget(QLabel("المورد:"), 0, 0)
        self.statement_supplier = QComboBox()
        self.statement_supplier.addItems(["اختر المورد", "شركة التقنية المتقدمة", "مؤسسة الجودة التجارية"])
        settings_layout.addWidget(self.statement_supplier, 0, 1)

        settings_layout.addWidget(QLabel("من تاريخ:"), 0, 2)
        self.statement_from_date = QDateEdit()
        self.statement_from_date.setDate(QDate.currentDate().addDays(-90))
        self.statement_from_date.setCalendarPopup(True)
        settings_layout.addWidget(self.statement_from_date, 0, 3)

        settings_layout.addWidget(QLabel("إلى تاريخ:"), 1, 0)
        self.statement_to_date = QDateEdit()
        self.statement_to_date.setDate(QDate.currentDate())
        self.statement_to_date.setCalendarPopup(True)
        settings_layout.addWidget(self.statement_to_date, 1, 1)

        generate_statement_btn = QPushButton("إنشاء كشف الحساب")
        generate_statement_btn.setStyleSheet(AppStyles.get_success_button_style())
        settings_layout.addWidget(generate_statement_btn, 1, 2)

        export_statement_btn = QPushButton("تصدير إلى Excel")
        export_statement_btn.setStyleSheet(AppStyles.get_button_style())
        settings_layout.addWidget(export_statement_btn, 1, 3)

        layout.addWidget(statement_settings_group)

        # جدول كشف الحساب
        self.statement_table = QTableWidget()
        self.statement_table.setColumnCount(7)
        self.statement_table.setHorizontalHeaderLabels([
            "التاريخ", "رقم المستند", "البيان", "مدين", "دائن", "الرصيد", "ملاحظات"
        ])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.statement_table)

        # إضافة بيانات تجريبية لكشف الحساب
        self.statement_table.setRowCount(7)
        statement_data = [
            ["2024-01-01", "رصيد افتتاحي", "الرصيد الافتتاحي", "", "", "0.00", ""],
            ["2024-01-15", "PUR-001", "فاتورة شراء أجهزة كمبيوتر", "15000.00", "", "15000.00", ""],
            ["2024-01-16", "PAY-001", "دفع فاتورة PUR-001", "", "15000.00", "0.00", "تحويل بنكي"],
            ["2024-01-20", "PUR-002", "فاتورة مواد مكتبية", "8500.00", "", "8500.00", ""],
            ["2024-01-22", "PAY-002", "دفعة جزئية", "", "5000.00", "3500.00", "نقد"],
            ["2024-01-25", "PUR-003", "فاتورة خوادم", "22000.00", "", "25500.00", ""],
            ["2024-02-01", "RET-001", "إرجاع مواد معيبة", "", "500.00", "25000.00", "خصم"]
        ]

        for row, data in enumerate(statement_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 3 and value:  # عمود المدين
                    item.setForeground(Qt.red)
                elif col == 4 and value:  # عمود الدائن
                    item.setForeground(Qt.darkGreen)
                elif col == 5:  # عمود الرصيد
                    font = item.font()
                    font.setBold(True)
                    item.setFont(font)
                self.statement_table.setItem(row, col, item)

        layout.addWidget(self.statement_table)

        # ملخص كشف الحساب
        summary_layout = QHBoxLayout()

        opening_balance = QLabel("الرصيد الافتتاحي: 0.00 ريال")
        opening_balance.setStyleSheet("font-weight: bold; color: #2c3e50;")
        summary_layout.addWidget(opening_balance)

        total_debit = QLabel("إجمالي المدين: 45,500.00 ريال")
        total_debit.setStyleSheet("font-weight: bold; color: #e74c3c;")
        summary_layout.addWidget(total_debit)

        total_credit = QLabel("إجمالي الدائن: 20,500.00 ريال")
        total_credit.setStyleSheet("font-weight: bold; color: #27ae60;")
        summary_layout.addWidget(total_credit)

        closing_balance = QLabel("الرصيد الختامي: 25,000.00 ريال")
        closing_balance.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        summary_layout.addWidget(closing_balance)

        summary_layout.addStretch()
        layout.addLayout(summary_layout)

        return widget
    
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        title = QLabel("تقارير الموردين")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # تبويبات فرعية للتقارير
        reports_sub_tabs = QTabWidget()
        reports_sub_tabs.setLayoutDirection(Qt.RightToLeft)
        reports_sub_tabs.setStyleSheet(AppStyles.get_tab_style())
        
        # تبويب التقارير المالية
        financial_reports_tab = self.create_financial_reports_tab()
        reports_sub_tabs.addTab(financial_reports_tab, "التقارير المالية")

        # تبويب تصدير البيانات
        export_tab = self.create_export_tab()
        reports_sub_tabs.addTab(export_tab, "تصدير البيانات")
        
        layout.addWidget(reports_sub_tabs)
        
        return widget

    def create_financial_reports_tab(self):
        """إنشاء تبويب التقارير المالية"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة أنواع التقارير المالية
        reports_types_group = QGroupBox("أنواع التقارير المالية")
        reports_types_group.setStyleSheet(AppStyles.get_frame_style())
        reports_types_layout = QGridLayout(reports_types_group)

        # تقرير أرصدة الموردين
        balances_report_btn = QPushButton("تقرير أرصدة الموردين")
        balances_report_btn.setStyleSheet(AppStyles.get_button_style())
        reports_types_layout.addWidget(balances_report_btn, 0, 0)

        # تقرير المشتريات حسب المورد
        purchases_by_supplier_btn = QPushButton("تقرير المشتريات حسب المورد")
        purchases_by_supplier_btn.setStyleSheet(AppStyles.get_button_style())
        reports_types_layout.addWidget(purchases_by_supplier_btn, 0, 1)

        # تقرير المدفوعات
        payments_report_btn = QPushButton("تقرير المدفوعات")
        payments_report_btn.setStyleSheet(AppStyles.get_button_style())
        reports_types_layout.addWidget(payments_report_btn, 1, 0)

        # تقرير الديون المستحقة
        outstanding_debts_btn = QPushButton("تقرير الديون المستحقة")
        outstanding_debts_btn.setStyleSheet(AppStyles.get_danger_button_style())
        reports_types_layout.addWidget(outstanding_debts_btn, 1, 1)

        layout.addWidget(reports_types_group)

        # مجموعة إعدادات التقرير
        report_settings_group = QGroupBox("إعدادات التقرير")
        report_settings_group.setStyleSheet(AppStyles.get_frame_style())
        settings_layout = QFormLayout(report_settings_group)

        self.report_from_date = QDateEdit()
        self.report_from_date.setDate(QDate.currentDate().addDays(-30))
        self.report_from_date.setCalendarPopup(True)
        settings_layout.addRow("من تاريخ:", self.report_from_date)

        self.report_to_date = QDateEdit()
        self.report_to_date.setDate(QDate.currentDate())
        self.report_to_date.setCalendarPopup(True)
        settings_layout.addRow("إلى تاريخ:", self.report_to_date)

        self.report_supplier = QComboBox()
        self.report_supplier.addItems(["جميع الموردين", "شركة التقنية المتقدمة", "مؤسسة الجودة التجارية"])
        settings_layout.addRow("المورد:", self.report_supplier)

        layout.addWidget(report_settings_group)

        # منطقة معاينة التقرير
        preview_group = QGroupBox("معاينة التقرير")
        preview_group.setStyleSheet(AppStyles.get_frame_style())
        preview_layout = QVBoxLayout(preview_group)

        self.report_preview = QTableWidget()
        self.report_preview.setColumnCount(6)
        self.report_preview.setHorizontalHeaderLabels([
            "رمز المورد", "اسم المورد", "إجمالي المشتريات", "إجمالي المدفوع", "الرصيد المستحق", "آخر معاملة"
        ])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.report_preview)

        # إضافة بيانات تجريبية للمعاينة
        self.report_preview.setRowCount(3)
        preview_data = [
            ["SUP-001", "شركة التقنية المتقدمة", "37,000.00", "21,400.00", "15,600.00", "2024-01-25"],
            ["SUP-002", "مؤسسة الجودة التجارية", "11,700.00", "8,200.00", "3,500.00", "2024-02-01"],
            ["", "الإجمالي", "48,700.00", "29,600.00", "19,100.00", ""]
        ]

        for row, data in enumerate(preview_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if row == 2:  # صف الإجمالي
                    font = item.font()
                    font.setBold(True)
                    item.setFont(font)
                    item.setBackground(Qt.lightGray)
                elif col == 4 and value and value != "الرصيد المستحق":  # عمود الرصيد المستحق
                    if "," in value and float(value.replace(",", "")) > 0:
                        item.setForeground(Qt.red)
                self.report_preview.setItem(row, col, item)

        preview_layout.addWidget(self.report_preview)
        layout.addWidget(preview_group)

        return widget

    def create_export_tab(self):
        """إنشاء تبويب تصدير البيانات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة خيارات التصدير
        export_options_group = QGroupBox("خيارات التصدير")
        export_options_group.setStyleSheet(AppStyles.get_frame_style())
        export_options_layout = QGridLayout(export_options_group)

        # تصدير بيانات الموردين
        export_suppliers_btn = QPushButton("تصدير بيانات الموردين")
        export_suppliers_btn.setStyleSheet(AppStyles.get_success_button_style())
        export_options_layout.addWidget(export_suppliers_btn, 0, 0)

        # تصدير المعاملات المالية
        export_transactions_btn = QPushButton("تصدير المعاملات المالية")
        export_transactions_btn.setStyleSheet(AppStyles.get_button_style())
        export_options_layout.addWidget(export_transactions_btn, 0, 1)

        # تصدير كشوف الحسابات
        export_statements_btn = QPushButton("تصدير كشوف الحسابات")
        export_statements_btn.setStyleSheet(AppStyles.get_button_style())
        export_options_layout.addWidget(export_statements_btn, 1, 0)

        # تصدير التقارير المالية
        export_reports_btn = QPushButton("تصدير التقارير المالية")
        export_reports_btn.setStyleSheet(AppStyles.get_primary_button_style())
        export_options_layout.addWidget(export_reports_btn, 1, 1)

        layout.addWidget(export_options_group)

        # مجموعة إعدادات التصدير
        export_settings_group = QGroupBox("إعدادات التصدير")
        export_settings_group.setStyleSheet(AppStyles.get_frame_style())
        export_settings_layout = QFormLayout(export_settings_group)

        self.export_format = QComboBox()
        self.export_format.addItems(["Excel (.xlsx)", "PDF (.pdf)", "CSV (.csv)"])
        export_settings_layout.addRow("تنسيق الملف:", self.export_format)

        self.export_from_date = QDateEdit()
        self.export_from_date.setDate(QDate.currentDate().addDays(-90))
        self.export_from_date.setCalendarPopup(True)
        export_settings_layout.addRow("من تاريخ:", self.export_from_date)

        self.export_to_date = QDateEdit()
        self.export_to_date.setDate(QDate.currentDate())
        self.export_to_date.setCalendarPopup(True)
        export_settings_layout.addRow("إلى تاريخ:", self.export_to_date)

        self.include_inactive = QCheckBox("تضمين الموردين غير النشطين")
        export_settings_layout.addRow("خيارات إضافية:", self.include_inactive)

        layout.addWidget(export_settings_group)

        # مجموعة معلومات التصدير
        export_info_group = QGroupBox("معلومات التصدير")
        export_info_group.setStyleSheet(AppStyles.get_frame_style())
        export_info_layout = QVBoxLayout(export_info_group)

        info_text = QLabel("""
        • تصدير بيانات الموردين: يشمل جميع البيانات الأساسية وبيانات الاتصال
        • تصدير المعاملات المالية: يشمل جميع الفواتير والمدفوعات
        • تصدير كشوف الحسابات: يشمل كشف حساب مفصل لكل مورد
        • تصدير التقارير المالية: يشمل التقارير المالية والإحصائيات
        """)
        info_text.setStyleSheet("color: #7f8c8d; padding: 10px;")
        info_text.setWordWrap(True)
        export_info_layout.addWidget(info_text)

        layout.addWidget(export_info_group)

        return widget
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.StyledPanel)
        buttons_frame.setStyleSheet(AppStyles.get_frame_style())
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        
        # الأزرار الأساسية
        self.add_btn = QPushButton("إضافة")
        self.add_btn.setStyleSheet(AppStyles.get_success_button_style())
        self.add_btn.clicked.connect(self.add_record)

        self.edit_btn = QPushButton("تعديل")
        self.edit_btn.setStyleSheet(AppStyles.get_button_style())
        self.edit_btn.clicked.connect(self.edit_record)

        self.save_btn = QPushButton("حفظ")
        self.save_btn.setStyleSheet(AppStyles.get_success_button_style())
        self.save_btn.clicked.connect(self.save_record)

        self.search_btn = QPushButton("بحث")
        self.search_btn.setStyleSheet(AppStyles.get_button_style())
        self.search_btn.clicked.connect(self.search_records)

        self.delete_btn = QPushButton("حذف")
        self.delete_btn.setStyleSheet(AppStyles.get_danger_button_style())
        self.delete_btn.clicked.connect(self.delete_record)

        self.exit_btn = QPushButton("خروج")
        self.exit_btn.setStyleSheet(AppStyles.get_primary_button_style())
        self.exit_btn.clicked.connect(self.close_suppliers)

        # إضافة الأزرار للتخطيط
        buttons_layout.addWidget(self.add_btn)
        buttons_layout.addWidget(self.edit_btn)
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.search_btn)
        buttons_layout.addWidget(self.delete_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.exit_btn)
        
        layout.addWidget(buttons_frame)

        # إضافة جدول الموردين
        self.add_suppliers_table(layout)

    def add_suppliers_table(self, layout):
        """إضافة جدول الموردين"""
        suppliers_table_group = QGroupBox("قائمة الموردين")
        suppliers_table_group.setStyleSheet(AppStyles.get_frame_style())
        suppliers_table_layout = QVBoxLayout(suppliers_table_group)

        # شريط البحث
        search_layout = QHBoxLayout()

        search_label = QLabel("البحث:")
        search_layout.addWidget(search_label)

        self.suppliers_search_input = QLineEdit()
        self.suppliers_search_input.setPlaceholderText("ابحث بالرمز أو الاسم أو رقم الهاتف...")
        search_layout.addWidget(self.suppliers_search_input)

        search_suppliers_btn = QPushButton("تصفية")
        search_suppliers_btn.setStyleSheet(AppStyles.get_button_style())
        search_layout.addWidget(search_suppliers_btn)

        clear_suppliers_search_btn = QPushButton("مسح")
        clear_suppliers_search_btn.setStyleSheet(AppStyles.get_warning_button_style())
        search_layout.addWidget(clear_suppliers_search_btn)

        suppliers_table_layout.addLayout(search_layout)

        # جدول الموردين
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(9)
        self.suppliers_table.setHorizontalHeaderLabels([
            "الرمز", "اسم المورد", "نوع المورد", "الهاتف", "البريد الإلكتروني",
            "إجمالي المشتريات", "الرصيد المستحق", "التقييم", "الحالة"
        ])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.suppliers_table)

        # إضافة بيانات تجريبية
        self.suppliers_table.setRowCount(4)
        suppliers_data = [
            ["SUP-001", "شركة التقنية المتقدمة", "محلي", "011-4567890", "<EMAIL>", "37,000.00", "15,600.00", "⭐⭐⭐⭐⭐", "نشط"],
            ["SUP-002", "مؤسسة الجودة التجارية", "محلي", "011-9876543", "<EMAIL>", "11,700.00", "3,500.00", "⭐⭐⭐⭐", "نشط"],
            ["SUP-003", "الشركة الدولية للاستيراد", "دولي", "011-5555555", "<EMAIL>", "0.00", "0.00", "⭐⭐⭐", "معلق"],
            ["SUP-004", "مؤسسة الخدمات المتكاملة", "خاص", "011-7777777", "<EMAIL>", "8,200.00", "0.00", "⭐⭐⭐⭐", "نشط"]
        ]

        for row, data in enumerate(suppliers_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 8:  # عمود الحالة
                    if value == "نشط":
                        item.setBackground(Qt.green)
                    elif value == "معلق":
                        item.setBackground(Qt.yellow)
                    elif value == "غير نشط":
                        item.setBackground(Qt.red)
                elif col == 6 and value != "الرصيد المستحق":  # عمود الرصيد المستحق
                    if value != "0.00" and "," in value and float(value.replace(",", "")) > 0:
                        item.setForeground(Qt.red)
                        font = item.font()
                        font.setBold(True)
                        item.setFont(font)
                self.suppliers_table.setItem(row, col, item)

        suppliers_table_layout.addWidget(self.suppliers_table)

        # إحصائيات الموردين
        suppliers_stats_layout = QHBoxLayout()

        total_suppliers_label = QLabel("إجمالي الموردين: 4")
        total_suppliers_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        suppliers_stats_layout.addWidget(total_suppliers_label)

        active_suppliers_label = QLabel("الموردين النشطين: 3")
        active_suppliers_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        suppliers_stats_layout.addWidget(active_suppliers_label)

        outstanding_suppliers_label = QLabel("موردين لديهم مستحقات: 2")
        outstanding_suppliers_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        suppliers_stats_layout.addWidget(outstanding_suppliers_label)

        total_outstanding_label = QLabel("إجمالي المستحقات: 19,100.00 ريال")
        total_outstanding_label.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 12px;")
        suppliers_stats_layout.addWidget(total_outstanding_label)

        suppliers_stats_layout.addStretch()
        suppliers_table_layout.addLayout(suppliers_stats_layout)

        layout.addWidget(suppliers_table_group)
    
    def show_sub_module(self, module_name):
        """عرض وحدة فرعية معينة"""
        tab_mapping = {
            "suppliers_data": 0,
            "suppliers_transactions": 1,
            "suppliers_reports": 2
        }
        
        if module_name in tab_mapping:
            self.tab_widget.setCurrentIndex(tab_mapping[module_name])
    
    def add_record(self):
        """إضافة سجل جديد"""
        current_tab = self.tab_widget.currentIndex()
        tab_name = self.tab_widget.tabText(current_tab)
        QMessageBox.information(self, "إضافة", f"إضافة سجل جديد في {tab_name}")
    
    def edit_record(self):
        """تعديل سجل"""
        current_tab = self.tab_widget.currentIndex()
        tab_name = self.tab_widget.tabText(current_tab)
        QMessageBox.information(self, "تعديل", f"تعديل سجل في {tab_name}")

    def save_record(self):
        """حفظ السجل"""
        current_tab = self.tab_widget.currentIndex()
        tab_name = self.tab_widget.tabText(current_tab)
        QMessageBox.information(self, "حفظ", f"تم حفظ البيانات في {tab_name} بنجاح")

    def search_records(self):
        """البحث في السجلات"""
        current_tab = self.tab_widget.currentIndex()
        tab_name = self.tab_widget.tabText(current_tab)
        QMessageBox.information(self, "بحث", f"البحث في {tab_name}")
    
    def delete_record(self):
        """حذف سجل"""
        current_tab = self.tab_widget.currentIndex()
        tab_name = self.tab_widget.tabText(current_tab)
        
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل تريد حذف السجل المحدد من {tab_name}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "حذف", f"تم حذف السجل من {tab_name}")
    
    def close_suppliers(self):
        """إغلاق نافذة الموردين"""
        self.parent().show_module("welcome")
