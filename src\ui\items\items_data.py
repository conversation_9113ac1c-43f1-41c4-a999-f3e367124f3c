"""
شاشة إدارة بيانات الأصناف
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QLineEdit, QComboBox, QCheckBox, QTextEdit,
                               QGroupBox, QPushButton, QFrame, QSpinBox, QDoubleSpinBox,
                               QMessageBox, QTableWidget, QTableWidgetItem, QTabWidget)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon, QPixmap

from src.ui.styles.app_styles import AppStyles
from src.utils.arabic_support import ArabicSupport


class ItemsDataWidget(QWidget):
    """شاشة إدارة بيانات الأصناف"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الشاشة
        title = QLabel("إدارة بيانات الأصناف")
        title.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                font-size: 18px; 
                color: #2c3e50; 
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()
        
        # الجانب الأيسر - نموذج البيانات
        self.create_form_section(content_layout)
        
        # الجانب الأيمن - جدول الأصناف
        self.create_table_section(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def create_form_section(self, layout):
        """إنشاء قسم النموذج"""
        form_frame = QFrame()
        form_frame.setMaximumWidth(450)
        form_layout = QVBoxLayout(form_frame)
        
        # تبويبات البيانات
        tabs = QTabWidget()
        
        # تبويب البيانات الأساسية
        basic_tab = QWidget()
        self.create_basic_info_tab(basic_tab)
        tabs.addTab(basic_tab, "البيانات الأساسية")
        
        # تبويب الأسعار والتكاليف
        pricing_tab = QWidget()
        self.create_pricing_tab(pricing_tab)
        tabs.addTab(pricing_tab, "الأسعار والتكاليف")
        
        # تبويب المخزون
        inventory_tab = QWidget()
        self.create_inventory_tab(inventory_tab)
        tabs.addTab(inventory_tab, "المخزون")
        
        form_layout.addWidget(tabs)
        layout.addWidget(form_frame)
    
    def create_basic_info_tab(self, tab):
        """إنشاء تبويب البيانات الأساسية"""
        layout = QVBoxLayout(tab)
        
        # مجموعة البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_group.setStyleSheet(AppStyles.get_frame_style())
        basic_layout = QFormLayout(basic_group)
        
        self.item_code = QLineEdit()
        self.item_code.setPlaceholderText("رمز الصنف")
        basic_layout.addRow("رمز الصنف:", self.item_code)
        
        self.item_name_ar = QLineEdit()
        self.item_name_ar.setPlaceholderText("اسم الصنف بالعربية")
        basic_layout.addRow("الاسم (عربي):", self.item_name_ar)
        
        self.item_name_en = QLineEdit()
        self.item_name_en.setPlaceholderText("Item Name in English")
        basic_layout.addRow("الاسم (إنجليزي):", self.item_name_en)
        
        self.item_group = QComboBox()
        self.item_group.addItems(["إلكترونيات", "ملابس", "أغذية", "مواد بناء"])
        basic_layout.addRow("المجموعة:", self.item_group)
        
        self.unit = QComboBox()
        self.unit.addItems(["قطعة", "كيلوجرام", "متر", "لتر"])
        basic_layout.addRow("الوحدة:", self.unit)
        
        self.barcode = QLineEdit()
        self.barcode.setPlaceholderText("الباركود")
        basic_layout.addRow("الباركود:", self.barcode)
        
        layout.addWidget(basic_group)
        
        # مجموعة الوصف والملاحظات
        desc_group = QGroupBox("الوصف والملاحظات")
        desc_group.setStyleSheet(AppStyles.get_frame_style())
        desc_layout = QVBoxLayout(desc_group)
        
        self.description = QTextEdit()
        self.description.setMaximumHeight(80)
        self.description.setPlaceholderText("وصف الصنف")
        desc_layout.addWidget(self.description)
        
        self.notes = QTextEdit()
        self.notes.setMaximumHeight(60)
        self.notes.setPlaceholderText("ملاحظات إضافية")
        desc_layout.addWidget(self.notes)
        
        layout.addWidget(desc_group)
        layout.addStretch()
    
    def create_pricing_tab(self, tab):
        """إنشاء تبويب الأسعار والتكاليف"""
        layout = QVBoxLayout(tab)
        
        # مجموعة التكاليف
        cost_group = QGroupBox("التكاليف")
        cost_group.setStyleSheet(AppStyles.get_frame_style())
        cost_layout = QFormLayout(cost_group)
        
        self.purchase_cost = QDoubleSpinBox()
        self.purchase_cost.setMaximum(999999.99)
        self.purchase_cost.setSuffix(" ريال")
        cost_layout.addRow("تكلفة الشراء:", self.purchase_cost)
        
        self.additional_cost = QDoubleSpinBox()
        self.additional_cost.setMaximum(999999.99)
        self.additional_cost.setSuffix(" ريال")
        cost_layout.addRow("التكاليف الإضافية:", self.additional_cost)
        
        self.total_cost = QDoubleSpinBox()
        self.total_cost.setMaximum(999999.99)
        self.total_cost.setSuffix(" ريال")
        self.total_cost.setReadOnly(True)
        cost_layout.addRow("إجمالي التكلفة:", self.total_cost)
        
        layout.addWidget(cost_group)
        
        # مجموعة الأسعار
        price_group = QGroupBox("أسعار البيع")
        price_group.setStyleSheet(AppStyles.get_frame_style())
        price_layout = QFormLayout(price_group)
        
        self.wholesale_price = QDoubleSpinBox()
        self.wholesale_price.setMaximum(999999.99)
        self.wholesale_price.setSuffix(" ريال")
        price_layout.addRow("سعر الجملة:", self.wholesale_price)
        
        self.retail_price = QDoubleSpinBox()
        self.retail_price.setMaximum(999999.99)
        self.retail_price.setSuffix(" ريال")
        price_layout.addRow("سعر التجزئة:", self.retail_price)
        
        self.special_price = QDoubleSpinBox()
        self.special_price.setMaximum(999999.99)
        self.special_price.setSuffix(" ريال")
        price_layout.addRow("السعر الخاص:", self.special_price)
        
        layout.addWidget(price_group)
        
        # مجموعة الضرائب والخصومات
        tax_group = QGroupBox("الضرائب والخصومات")
        tax_group.setStyleSheet(AppStyles.get_frame_style())
        tax_layout = QFormLayout(tax_group)
        
        self.tax_rate = QDoubleSpinBox()
        self.tax_rate.setMaximum(100.0)
        self.tax_rate.setSuffix(" %")
        self.tax_rate.setValue(15.0)
        tax_layout.addRow("معدل الضريبة:", self.tax_rate)
        
        self.discount_rate = QDoubleSpinBox()
        self.discount_rate.setMaximum(100.0)
        self.discount_rate.setSuffix(" %")
        tax_layout.addRow("معدل الخصم:", self.discount_rate)
        
        layout.addWidget(tax_group)
        layout.addStretch()
    
    def create_inventory_tab(self, tab):
        """إنشاء تبويب المخزون"""
        layout = QVBoxLayout(tab)
        
        # مجموعة المخزون الحالي
        current_group = QGroupBox("المخزون الحالي")
        current_group.setStyleSheet(AppStyles.get_frame_style())
        current_layout = QFormLayout(current_group)
        
        self.current_stock = QSpinBox()
        self.current_stock.setMaximum(999999)
        current_layout.addRow("الكمية الحالية:", self.current_stock)
        
        self.reserved_stock = QSpinBox()
        self.reserved_stock.setMaximum(999999)
        current_layout.addRow("الكمية المحجوزة:", self.reserved_stock)
        
        self.available_stock = QSpinBox()
        self.available_stock.setMaximum(999999)
        self.available_stock.setReadOnly(True)
        current_layout.addRow("الكمية المتاحة:", self.available_stock)
        
        layout.addWidget(current_group)
        
        # مجموعة حدود المخزون
        limits_group = QGroupBox("حدود المخزون")
        limits_group.setStyleSheet(AppStyles.get_frame_style())
        limits_layout = QFormLayout(limits_group)
        
        self.min_stock = QSpinBox()
        self.min_stock.setMaximum(999999)
        limits_layout.addRow("الحد الأدنى:", self.min_stock)
        
        self.max_stock = QSpinBox()
        self.max_stock.setMaximum(999999)
        limits_layout.addRow("الحد الأقصى:", self.max_stock)
        
        self.reorder_point = QSpinBox()
        self.reorder_point.setMaximum(999999)
        limits_layout.addRow("نقطة إعادة الطلب:", self.reorder_point)
        
        layout.addWidget(limits_group)
        
        # مجموعة الإعدادات
        settings_group = QGroupBox("إعدادات المخزون")
        settings_group.setStyleSheet(AppStyles.get_frame_style())
        settings_layout = QVBoxLayout(settings_group)
        
        self.track_serial = QCheckBox("تتبع الأرقام التسلسلية")
        settings_layout.addWidget(self.track_serial)
        
        self.track_expiry = QCheckBox("تتبع تاريخ الانتهاء")
        settings_layout.addWidget(self.track_expiry)
        
        self.allow_negative = QCheckBox("السماح بالمخزون السالب")
        settings_layout.addWidget(self.allow_negative)
        
        self.is_active = QCheckBox("صنف نشط")
        self.is_active.setChecked(True)
        settings_layout.addWidget(self.is_active)
        
        layout.addWidget(settings_group)
        layout.addStretch()
    
    def create_table_section(self, layout):
        """إنشاء قسم الجدول"""
        table_frame = QFrame()
        table_layout = QVBoxLayout(table_frame)
        
        # عنوان الجدول
        table_title = QLabel("الأصناف المسجلة")
        table_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        table_layout.addWidget(table_title)
        
        # الجدول
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(8)
        self.items_table.setHorizontalHeaderLabels([
            "الرمز", "الاسم", "المجموعة", "الوحدة", "المخزون", "سعر البيع", "التكلفة", "الحالة"
        ])
        
        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.items_table)
        
        # إضافة بيانات تجريبية
        self.items_table.setRowCount(10)
        items_data = [
            ["ITM001", "لابتوب ديل", "إلكترونيات", "قطعة", "25", "3500.00", "2800.00", "نشط"],
            ["ITM002", "قميص قطني", "ملابس", "قطعة", "150", "85.00", "45.00", "نشط"],
            ["ITM003", "أرز بسمتي", "أغذية", "كيلوجرام", "500", "12.50", "8.00", "نشط"],
            ["ITM004", "أسمنت", "مواد بناء", "كيس", "200", "25.00", "18.00", "نشط"],
            ["ITM005", "هاتف ذكي", "إلكترونيات", "قطعة", "45", "1200.00", "950.00", "نشط"],
            ["ITM006", "فستان صيفي", "ملابس", "قطعة", "80", "120.00", "65.00", "نشط"],
            ["ITM007", "زيت زيتون", "أغذية", "لتر", "300", "35.00", "22.00", "نشط"],
            ["ITM008", "طلاء جداري", "مواد بناء", "جالون", "75", "45.00", "28.00", "نشط"],
            ["ITM009", "ساعة يد", "إكسسوارات", "قطعة", "60", "250.00", "180.00", "نشط"],
            ["ITM010", "شاي أخضر", "أغذية", "علبة", "400", "18.00", "12.00", "نشط"]
        ]
        
        for row, data in enumerate(items_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 2:  # عمود المجموعة
                    if value == "إلكترونيات":
                        item.setBackground(AppStyles.get_color("primary_light"))
                    elif value == "ملابس":
                        item.setBackground(AppStyles.get_color("info_light"))
                    elif value == "أغذية":
                        item.setBackground(AppStyles.get_color("success_light"))
                    else:
                        item.setBackground(AppStyles.get_color("warning_light"))
                elif col == 7:  # عمود الحالة
                    if value == "نشط":
                        item.setBackground(AppStyles.get_color("success_light"))
                    else:
                        item.setBackground(AppStyles.get_color("secondary_light"))
                
                self.items_table.setItem(row, col, item)
        
        table_layout.addWidget(self.items_table)
        layout.addWidget(table_frame)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر إضافة
        add_btn = QPushButton("إضافة صنف")
        add_btn.setIcon(QIcon("assets/icons/add.png"))
        add_btn.clicked.connect(self.add_item)
        add_btn.setStyleSheet(AppStyles.get_button_style("primary"))
        buttons_layout.addWidget(add_btn)
        
        # زر تعديل
        edit_btn = QPushButton("تعديل")
        edit_btn.setIcon(QIcon("assets/icons/edit.png"))
        edit_btn.clicked.connect(self.edit_item)
        edit_btn.setStyleSheet(AppStyles.get_button_style("info"))
        buttons_layout.addWidget(edit_btn)
        
        # زر حفظ
        save_btn = QPushButton("حفظ")
        save_btn.setIcon(QIcon("assets/icons/save.png"))
        save_btn.clicked.connect(self.save_item)
        save_btn.setStyleSheet(AppStyles.get_button_style("success"))
        buttons_layout.addWidget(save_btn)
        
        # زر حذف
        delete_btn = QPushButton("حذف")
        delete_btn.setIcon(QIcon("assets/icons/delete.png"))
        delete_btn.clicked.connect(self.delete_item)
        delete_btn.setStyleSheet(AppStyles.get_button_style("danger"))
        buttons_layout.addWidget(delete_btn)
        
        buttons_layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setIcon(QIcon("assets/icons/close.png"))
        close_btn.clicked.connect(self.close_screen)
        close_btn.setStyleSheet(AppStyles.get_button_style("secondary"))
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)
    
    def add_item(self):
        """إضافة صنف جديد"""
        if not self.item_code.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز الصنف")
            return
        
        if not self.item_name_ar.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الصنف")
            return
        
        QMessageBox.information(self, "نجح", "تم إضافة الصنف بنجاح!")
        self.clear_form()
    
    def edit_item(self):
        """تعديل صنف"""
        current_row = self.items_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار صنف للتعديل")
            return
        
        self.load_item_data(current_row)
    
    def save_item(self):
        """حفظ الصنف"""
        try:
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ بيانات الصنف بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
    
    def delete_item(self):
        """حذف صنف"""
        current_row = self.items_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار صنف للحذف")
            return
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   "هل تريد حذف الصنف المختار؟")
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الحذف", "تم حذف الصنف بنجاح!")
    
    def load_item_data(self, row):
        """تحميل بيانات الصنف"""
        self.item_code.setText(self.items_table.item(row, 0).text())
        self.item_name_ar.setText(self.items_table.item(row, 1).text())
        self.current_stock.setValue(int(self.items_table.item(row, 4).text()))
        self.retail_price.setValue(float(self.items_table.item(row, 5).text()))
        self.purchase_cost.setValue(float(self.items_table.item(row, 6).text()))
    
    def clear_form(self):
        """مسح النموذج"""
        self.item_code.clear()
        self.item_name_ar.clear()
        self.item_name_en.clear()
        self.description.clear()
        self.notes.clear()
        self.barcode.clear()
        self.current_stock.setValue(0)
        self.purchase_cost.setValue(0.0)
        self.retail_price.setValue(0.0)
        self.is_active.setChecked(True)
    
    def close_screen(self):
        """إغلاق الشاشة"""
        if hasattr(self, 'main_window'):
            self.window().close()
        else:
            self.parent().close()
