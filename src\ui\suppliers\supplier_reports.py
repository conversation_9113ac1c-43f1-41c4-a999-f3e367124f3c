"""
شاشة تقارير الموردين
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QLineEdit, QComboBox, QDateEdit, QPushButton,
                               QGroupBox, QFrame, QTabWidget, QTableWidget, QTableWidgetItem,
                               QMessageBox, QTextEdit, QProgressBar, QCheckBox)
from PySide6.QtCore import Qt, QDate, QTimer
from PySide6.QtGui import QIcon, QPixmap
from PySide6.QtCharts import QChart, QChartView, QPieSeries, QBarSeries, QBarSet

from src.utils.styles import AppStyles
from src.utils.arabic_support import ArabicSupport


class SupplierReportsWidget(QWidget):
    """شاشة تقارير الموردين"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الشاشة
        title = QLabel("تقارير الموردين")
        title.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                font-size: 18px; 
                color: #2c3e50; 
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # تبويبات التقارير
        tabs = QTabWidget()
        
        # تبويب التقارير المالية
        financial_tab = QWidget()
        self.create_financial_reports_tab(financial_tab)
        tabs.addTab(financial_tab, "التقارير المالية")
        
        # تبويب تقارير الأداء
        performance_tab = QWidget()
        self.create_performance_reports_tab(performance_tab)
        tabs.addTab(performance_tab, "تقارير الأداء")
        
        # تبويب التقارير التحليلية
        analytics_tab = QWidget()
        self.create_analytics_reports_tab(analytics_tab)
        tabs.addTab(analytics_tab, "التقارير التحليلية")
        
        main_layout.addWidget(tabs)
        
        # أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def create_financial_reports_tab(self, tab):
        """إنشاء تبويب التقارير المالية"""
        layout = QHBoxLayout(tab)
        
        # الجانب الأيسر - معايير التقرير
        criteria_frame = QFrame()
        criteria_frame.setMaximumWidth(350)
        criteria_layout = QVBoxLayout(criteria_frame)
        
        # مجموعة الفترة الزمنية
        period_group = QGroupBox("الفترة الزمنية")
        period_group.setStyleSheet(AppStyles.get_frame_style())
        period_layout = QFormLayout(period_group)
        
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addMonths(-1))
        self.from_date.setCalendarPopup(True)
        period_layout.addRow("من تاريخ:", self.from_date)
        
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        self.to_date.setCalendarPopup(True)
        period_layout.addRow("إلى تاريخ:", self.to_date)
        
        criteria_layout.addWidget(period_group)
        
        # مجموعة المعايير
        filter_group = QGroupBox("معايير التقرير")
        filter_group.setStyleSheet(AppStyles.get_frame_style())
        filter_layout = QFormLayout(filter_group)
        
        self.supplier_filter = QComboBox()
        self.supplier_filter.addItems([
            "جميع الموردين", "شركة التقنية المتقدمة", "مؤسسة الأزياء العصرية",
            "مصنع الأغذية الطازجة", "شركة مواد البناء"
        ])
        filter_layout.addRow("المورد:", self.supplier_filter)
        
        self.transaction_type = QComboBox()
        self.transaction_type.addItems([
            "جميع المعاملات", "فواتير الشراء", "المردودات", "الدفعات"
        ])
        filter_layout.addRow("نوع المعاملة:", self.transaction_type)
        
        self.payment_status = QComboBox()
        self.payment_status.addItems([
            "جميع الحالات", "مدفوع", "غير مدفوع", "مدفوع جزئياً", "متأخر"
        ])
        filter_layout.addRow("حالة الدفع:", self.payment_status)
        
        criteria_layout.addWidget(filter_group)
        
        # مجموعة خيارات التقرير
        options_group = QGroupBox("خيارات التقرير")
        options_group.setStyleSheet(AppStyles.get_frame_style())
        options_layout = QVBoxLayout(options_group)
        
        self.include_details = QCheckBox("تضمين التفاصيل")
        self.include_details.setChecked(True)
        options_layout.addWidget(self.include_details)
        
        self.include_charts = QCheckBox("تضمين الرسوم البيانية")
        self.include_charts.setChecked(True)
        options_layout.addWidget(self.include_charts)
        
        self.group_by_supplier = QCheckBox("تجميع حسب المورد")
        self.group_by_supplier.setChecked(True)
        options_layout.addWidget(self.group_by_supplier)
        
        criteria_layout.addWidget(options_group)
        
        # زر إنشاء التقرير
        generate_btn = QPushButton("إنشاء التقرير")
        generate_btn.setIcon(QIcon("assets/icons/report.png"))
        generate_btn.clicked.connect(self.generate_financial_report)
        generate_btn.setStyleSheet(AppStyles.get_button_style("primary"))
        criteria_layout.addWidget(generate_btn)
        
        criteria_layout.addStretch()
        layout.addWidget(criteria_frame)
        
        # الجانب الأيمن - نتائج التقرير
        results_frame = QFrame()
        results_layout = QVBoxLayout(results_frame)
        
        # عنوان النتائج
        results_title = QLabel("نتائج التقرير المالي")
        results_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        results_layout.addWidget(results_title)
        
        # جدول النتائج
        self.financial_table = QTableWidget()
        self.financial_table.setColumnCount(6)
        self.financial_table.setHorizontalHeaderLabels([
            "المورد", "إجمالي المشتريات", "المدفوع", "المتبقي", "عدد المعاملات", "متوسط المعاملة"
        ])
        
        AppStyles.setup_table_widget(self.financial_table)
        
        # بيانات تجريبية
        self.financial_table.setRowCount(5)
        financial_data = [
            ["شركة التقنية المتقدمة", "44,500.00", "37,000.00", "7,500.00", "3", "14,833.33"],
            ["مؤسسة الأزياء العصرية", "15,800.00", "11,800.00", "4,000.00", "3", "5,266.67"],
            ["مصنع الأغذية الطازجة", "26,500.00", "14,500.00", "12,000.00", "3", "8,833.33"],
            ["شركة مواد البناء", "37,300.00", "29,300.00", "8,000.00", "3", "12,433.33"],
            ["الإجمالي", "124,100.00", "92,600.00", "31,500.00", "12", "10,341.67"]
        ]
        
        for row, data in enumerate(financial_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if row == 4:  # صف الإجمالي
                    item.setBackground(AppStyles.get_color("primary_light"))
                self.financial_table.setItem(row, col, item)
        
        results_layout.addWidget(self.financial_table)
        
        # ملخص إحصائي
        summary_frame = QFrame()
        summary_frame.setStyleSheet(AppStyles.get_frame_style())
        summary_layout = QHBoxLayout(summary_frame)
        
        # إحصائيات سريعة
        stats_labels = [
            ("إجمالي المشتريات:", "124,100.00 ريال"),
            ("إجمالي المدفوع:", "92,600.00 ريال"),
            ("إجمالي المتبقي:", "31,500.00 ريال"),
            ("عدد الموردين:", "4 موردين")
        ]
        
        for label_text, value_text in stats_labels:
            stat_frame = QFrame()
            stat_layout = QVBoxLayout(stat_frame)
            
            label = QLabel(label_text)
            label.setStyleSheet("font-weight: bold; color: #2c3e50;")
            stat_layout.addWidget(label)
            
            value = QLabel(value_text)
            value.setStyleSheet("font-size: 16px; color: #27ae60;")
            stat_layout.addWidget(value)
            
            summary_layout.addWidget(stat_frame)
        
        results_layout.addWidget(summary_frame)
        layout.addWidget(results_frame)
    
    def create_performance_reports_tab(self, tab):
        """إنشاء تبويب تقارير الأداء"""
        layout = QVBoxLayout(tab)
        
        # شريط أدوات التقرير
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        period_label = QLabel("فترة التقرير:")
        toolbar_layout.addWidget(period_label)
        
        self.performance_period = QComboBox()
        self.performance_period.addItems(["الشهر الحالي", "الربع الحالي", "السنة الحالية", "مخصص"])
        toolbar_layout.addWidget(self.performance_period)
        
        refresh_btn = QPushButton("تحديث")
        refresh_btn.setIcon(QIcon("assets/icons/refresh.png"))
        refresh_btn.clicked.connect(self.refresh_performance_data)
        refresh_btn.setStyleSheet(AppStyles.get_button_style("info"))
        toolbar_layout.addWidget(refresh_btn)
        
        toolbar_layout.addStretch()
        layout.addWidget(toolbar_frame)
        
        # جدول تقييم الأداء
        performance_title = QLabel("تقييم أداء الموردين")
        performance_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        layout.addWidget(performance_title)
        
        self.performance_table = QTableWidget()
        self.performance_table.setColumnCount(8)
        self.performance_table.setHorizontalHeaderLabels([
            "المورد", "التقييم العام", "جودة المنتجات", "سرعة التسليم", 
            "الالتزام بالمواعيد", "خدمة العملاء", "التنافسية", "الموثوقية"
        ])
        
        AppStyles.setup_table_widget(self.performance_table)
        
        # بيانات تجريبية للأداء
        self.performance_table.setRowCount(4)
        performance_data = [
            ["شركة التقنية المتقدمة", "ممتاز", "95%", "90%", "88%", "92%", "85%", "94%"],
            ["مؤسسة الأزياء العصرية", "جيد جداً", "88%", "85%", "90%", "87%", "92%", "89%"],
            ["مصنع الأغذية الطازجة", "جيد", "82%", "78%", "85%", "80%", "88%", "83%"],
            ["شركة مواد البناء", "ممتاز", "93%", "95%", "92%", "90%", "87%", "91%"]
        ]
        
        for row, data in enumerate(performance_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 1:  # عمود التقييم العام
                    if value == "ممتاز":
                        item.setBackground(AppStyles.get_color("success_light"))
                    elif value == "جيد جداً":
                        item.setBackground(AppStyles.get_color("info_light"))
                    else:
                        item.setBackground(AppStyles.get_color("warning_light"))
                self.performance_table.setItem(row, col, item)
        
        layout.addWidget(self.performance_table)
        
        # مؤشرات الأداء الرئيسية
        kpi_frame = QFrame()
        kpi_frame.setStyleSheet(AppStyles.get_frame_style())
        kpi_layout = QHBoxLayout(kpi_frame)
        
        kpi_data = [
            ("متوسط التقييم العام", "89.5%", "success"),
            ("معدل الالتزام بالمواعيد", "88.8%", "info"),
            ("متوسط جودة المنتجات", "89.5%", "primary"),
            ("معدل رضا العملاء", "87.3%", "warning")
        ]
        
        for title, value, color in kpi_data:
            kpi_item = QFrame()
            kpi_item.setStyleSheet(f"""
                QFrame {{
                    background-color: {AppStyles.get_color(f"{color}_light").name()};
                    border-radius: 8px;
                    padding: 15px;
                    margin: 5px;
                }}
            """)
            kpi_item_layout = QVBoxLayout(kpi_item)
            
            title_label = QLabel(title)
            title_label.setStyleSheet("font-weight: bold; font-size: 12px;")
            title_label.setAlignment(Qt.AlignCenter)
            kpi_item_layout.addWidget(title_label)
            
            value_label = QLabel(value)
            value_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50;")
            value_label.setAlignment(Qt.AlignCenter)
            kpi_item_layout.addWidget(value_label)
            
            kpi_layout.addWidget(kpi_item)
        
        layout.addWidget(kpi_frame)
    
    def create_analytics_reports_tab(self, tab):
        """إنشاء تبويب التقارير التحليلية"""
        layout = QVBoxLayout(tab)
        
        # شريط أدوات التحليل
        analytics_toolbar = QFrame()
        analytics_toolbar_layout = QHBoxLayout(analytics_toolbar)
        
        analysis_type_label = QLabel("نوع التحليل:")
        analytics_toolbar_layout.addWidget(analysis_type_label)
        
        self.analysis_type = QComboBox()
        self.analysis_type.addItems([
            "تحليل الاتجاهات", "تحليل المخاطر", "تحليل التكاليف", 
            "تحليل الموسمية", "تحليل التنبؤات"
        ])
        analytics_toolbar_layout.addWidget(self.analysis_type)
        
        analyze_btn = QPushButton("تحليل")
        analyze_btn.setIcon(QIcon("assets/icons/analytics.png"))
        analyze_btn.clicked.connect(self.run_analysis)
        analyze_btn.setStyleSheet(AppStyles.get_button_style("primary"))
        analytics_toolbar_layout.addWidget(analyze_btn)
        
        analytics_toolbar_layout.addStretch()
        layout.addWidget(analytics_toolbar)
        
        # منطقة النتائج
        results_area = QFrame()
        results_area.setStyleSheet(AppStyles.get_frame_style())
        results_layout = QVBoxLayout(results_area)
        
        # عنوان النتائج
        results_title = QLabel("نتائج التحليل")
        results_title.setStyleSheet("font-weight: bold; font-size: 16px; color: #2c3e50; padding: 10px;")
        results_layout.addWidget(results_title)
        
        # منطقة النص للنتائج
        self.analysis_results = QTextEdit()
        self.analysis_results.setReadOnly(True)
        self.analysis_results.setPlainText("""
تحليل الاتجاهات - تقرير شهر فبراير 2024

الملاحظات الرئيسية:
• زيادة في حجم المشتريات بنسبة 15% مقارنة بالشهر السابق
• تحسن في معدلات الدفع بنسبة 8%
• انخفاض في متوسط وقت التسليم بنسبة 12%

التوصيات:
• الاستمرار في التعامل مع الموردين ذوي الأداء المتميز
• مراجعة شروط الدفع مع الموردين المتأخرين
• تطوير علاقات أقوى مع الموردين الجدد

المخاطر المحتملة:
• تأخير في التسليم من مورد واحد
• ارتفاع في أسعار المواد الخام
• تقلبات في أسعار الصرف
        """)
        results_layout.addWidget(self.analysis_results)
        
        layout.addWidget(results_area)
        
        # شريط التقدم للتحليل
        self.analysis_progress = QProgressBar()
        self.analysis_progress.setVisible(False)
        layout.addWidget(self.analysis_progress)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر تصدير
        export_btn = QPushButton("تصدير التقرير")
        export_btn.setIcon(QIcon("assets/icons/export.png"))
        export_btn.clicked.connect(self.export_report)
        export_btn.setStyleSheet(AppStyles.get_button_style("success"))
        buttons_layout.addWidget(export_btn)
        
        # زر طباعة
        print_btn = QPushButton("طباعة")
        print_btn.setIcon(QIcon("assets/icons/print.png"))
        print_btn.clicked.connect(self.print_report)
        print_btn.setStyleSheet(AppStyles.get_button_style("warning"))
        buttons_layout.addWidget(print_btn)
        
        # زر إرسال بالبريد
        email_btn = QPushButton("إرسال بالبريد")
        email_btn.setIcon(QIcon("assets/icons/email.png"))
        email_btn.clicked.connect(self.email_report)
        email_btn.setStyleSheet(AppStyles.get_button_style("info"))
        buttons_layout.addWidget(email_btn)
        
        buttons_layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setIcon(QIcon("assets/icons/close.png"))
        close_btn.clicked.connect(self.close_screen)
        close_btn.setStyleSheet(AppStyles.get_button_style("secondary"))
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)
    
    def generate_financial_report(self):
        """إنشاء التقرير المالي"""
        QMessageBox.information(self, "تم إنشاء التقرير", "تم إنشاء التقرير المالي بنجاح!")
    
    def refresh_performance_data(self):
        """تحديث بيانات الأداء"""
        QMessageBox.information(self, "تم التحديث", "تم تحديث بيانات الأداء!")
    
    def run_analysis(self):
        """تشغيل التحليل"""
        self.analysis_progress.setVisible(True)
        self.analysis_progress.setValue(0)
        
        # محاكاة عملية التحليل
        timer = QTimer()
        timer.timeout.connect(lambda: self.update_analysis_progress(timer))
        timer.start(100)
    
    def update_analysis_progress(self, timer):
        """تحديث شريط التقدم"""
        current_value = self.analysis_progress.value()
        if current_value < 100:
            self.analysis_progress.setValue(current_value + 5)
        else:
            timer.stop()
            self.analysis_progress.setVisible(False)
            QMessageBox.information(self, "اكتمل التحليل", "تم إكمال التحليل بنجاح!")
    
    def export_report(self):
        """تصدير التقرير"""
        QMessageBox.information(self, "تصدير", "تم تصدير التقرير بنجاح!")
    
    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "طباعة", "تم إرسال التقرير للطباعة!")
    
    def email_report(self):
        """إرسال التقرير بالبريد الإلكتروني"""
        QMessageBox.information(self, "إرسال", "تم إرسال التقرير بالبريد الإلكتروني!")
    
    def close_screen(self):
        """إغلاق الشاشة"""
        if hasattr(self, 'main_window'):
            self.window().close()
        else:
            self.parent().close()
