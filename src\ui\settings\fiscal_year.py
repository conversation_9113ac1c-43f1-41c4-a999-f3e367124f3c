"""
شاشة إدارة السنة المالية
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QLineEdit, QDateEdit, QComboBox, QCheckBox,
                               QGroupBox, QScrollArea, QPushButton, QFrame,
                               QMessageBox, QTableWidget, QTableWidgetItem,
                               QHeaderView)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QIcon

from src.utils.styles import AppStyles
from src.utils.arabic_support import ArabicSupport


class FiscalYearWidget(QWidget):
    """شاشة إدارة السنة المالية"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الشاشة
        title = QLabel("إدارة السنة المالية")
        title.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                font-size: 18px; 
                color: #2c3e50; 
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()
        
        # الجانب الأيسر - نموذج البيانات
        self.create_form_section(content_layout)
        
        # الجانب الأيمن - جدول السنوات المالية
        self.create_table_section(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def create_form_section(self, layout):
        """إنشاء قسم النموذج"""
        form_frame = QFrame()
        form_frame.setMaximumWidth(400)
        form_layout = QVBoxLayout(form_frame)
        
        # مجموعة بيانات السنة المالية
        fiscal_group = QGroupBox("بيانات السنة المالية")
        fiscal_group.setStyleSheet(AppStyles.get_frame_style())
        fiscal_layout = QFormLayout(fiscal_group)
        
        self.year_code = QLineEdit()
        self.year_code.setPlaceholderText("رمز السنة المالية")
        fiscal_layout.addRow("رمز السنة:", self.year_code)
        
        self.year_name = QLineEdit()
        self.year_name.setPlaceholderText("اسم السنة المالية")
        fiscal_layout.addRow("اسم السنة:", self.year_name)
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate())
        self.start_date.setCalendarPopup(True)
        fiscal_layout.addRow("تاريخ البداية:", self.start_date)
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate().addYears(1))
        self.end_date.setCalendarPopup(True)
        fiscal_layout.addRow("تاريخ النهاية:", self.end_date)
        
        self.is_active = QCheckBox("سنة نشطة")
        fiscal_layout.addRow("الحالة:", self.is_active)
        
        self.is_closed = QCheckBox("سنة مغلقة")
        fiscal_layout.addRow("الإغلاق:", self.is_closed)
        
        form_layout.addWidget(fiscal_group)
        
        # مجموعة الإعدادات الإضافية
        settings_group = QGroupBox("إعدادات إضافية")
        settings_group.setStyleSheet(AppStyles.get_frame_style())
        settings_layout = QFormLayout(settings_group)
        
        self.auto_close = QCheckBox("إغلاق تلقائي في نهاية السنة")
        settings_layout.addRow("الإغلاق التلقائي:", self.auto_close)
        
        self.backup_before_close = QCheckBox("نسخ احتياطي قبل الإغلاق")
        self.backup_before_close.setChecked(True)
        settings_layout.addRow("النسخ الاحتياطي:", self.backup_before_close)
        
        self.archive_data = QCheckBox("أرشفة البيانات بعد الإغلاق")
        settings_layout.addRow("الأرشفة:", self.archive_data)
        
        form_layout.addWidget(settings_group)
        form_layout.addStretch()
        
        layout.addWidget(form_frame)
    
    def create_table_section(self, layout):
        """إنشاء قسم الجدول"""
        table_frame = QFrame()
        table_layout = QVBoxLayout(table_frame)
        
        # عنوان الجدول
        table_title = QLabel("السنوات المالية المسجلة")
        table_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        table_layout.addWidget(table_title)
        
        # الجدول
        self.fiscal_years_table = QTableWidget()
        self.fiscal_years_table.setColumnCount(6)
        self.fiscal_years_table.setHorizontalHeaderLabels([
            "رمز السنة", "اسم السنة", "تاريخ البداية", "تاريخ النهاية", "الحالة", "الإغلاق"
        ])
        
        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.fiscal_years_table)
        
        # إضافة بيانات تجريبية
        self.fiscal_years_table.setRowCount(4)
        fiscal_data = [
            ["FY2024", "السنة المالية 2024", "2024-01-01", "2024-12-31", "نشطة", "مفتوحة"],
            ["FY2023", "السنة المالية 2023", "2023-01-01", "2023-12-31", "غير نشطة", "مغلقة"],
            ["FY2022", "السنة المالية 2022", "2022-01-01", "2022-12-31", "غير نشطة", "مغلقة"],
            ["FY2021", "السنة المالية 2021", "2021-01-01", "2021-12-31", "غير نشطة", "مغلقة"]
        ]
        
        for row, data in enumerate(fiscal_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 4:  # عمود الحالة
                    if value == "نشطة":
                        item.setBackground(AppStyles.get_color("success_light"))
                    else:
                        item.setBackground(AppStyles.get_color("secondary_light"))
                elif col == 5:  # عمود الإغلاق
                    if value == "مغلقة":
                        item.setBackground(AppStyles.get_color("danger_light"))
                    else:
                        item.setBackground(AppStyles.get_color("warning_light"))
                
                self.fiscal_years_table.setItem(row, col, item)
        
        table_layout.addWidget(self.fiscal_years_table)
        layout.addWidget(table_frame)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر إضافة
        add_btn = QPushButton("إضافة سنة مالية")
        add_btn.setIcon(QIcon("assets/icons/add.png"))
        add_btn.clicked.connect(self.add_fiscal_year)
        add_btn.setStyleSheet(AppStyles.get_button_style("primary"))
        buttons_layout.addWidget(add_btn)
        
        # زر تعديل
        edit_btn = QPushButton("تعديل")
        edit_btn.setIcon(QIcon("assets/icons/edit.png"))
        edit_btn.clicked.connect(self.edit_fiscal_year)
        edit_btn.setStyleSheet(AppStyles.get_button_style("info"))
        buttons_layout.addWidget(edit_btn)
        
        # زر حفظ
        save_btn = QPushButton("حفظ")
        save_btn.setIcon(QIcon("assets/icons/save.png"))
        save_btn.clicked.connect(self.save_fiscal_year)
        save_btn.setStyleSheet(AppStyles.get_button_style("success"))
        buttons_layout.addWidget(save_btn)
        
        # زر حذف
        delete_btn = QPushButton("حذف")
        delete_btn.setIcon(QIcon("assets/icons/delete.png"))
        delete_btn.clicked.connect(self.delete_fiscal_year)
        delete_btn.setStyleSheet(AppStyles.get_button_style("danger"))
        buttons_layout.addWidget(delete_btn)
        
        # زر إغلاق السنة
        close_year_btn = QPushButton("إغلاق السنة")
        close_year_btn.setIcon(QIcon("assets/icons/lock.png"))
        close_year_btn.clicked.connect(self.close_fiscal_year)
        close_year_btn.setStyleSheet(AppStyles.get_button_style("warning"))
        buttons_layout.addWidget(close_year_btn)
        
        buttons_layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setIcon(QIcon("assets/icons/close.png"))
        close_btn.clicked.connect(self.close_screen)
        close_btn.setStyleSheet(AppStyles.get_button_style("secondary"))
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)
    
    def add_fiscal_year(self):
        """إضافة سنة مالية جديدة"""
        if not self.year_code.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز السنة المالية")
            return
        
        if not self.year_name.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم السنة المالية")
            return
        
        # إضافة السنة المالية الجديدة
        QMessageBox.information(self, "نجح", "تم إضافة السنة المالية بنجاح!")
        self.clear_form()
    
    def edit_fiscal_year(self):
        """تعديل سنة مالية"""
        current_row = self.fiscal_years_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار سنة مالية للتعديل")
            return
        
        # تحميل بيانات السنة المالية المختارة
        self.load_fiscal_year_data(current_row)
    
    def save_fiscal_year(self):
        """حفظ السنة المالية"""
        try:
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ بيانات السنة المالية بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
    
    def delete_fiscal_year(self):
        """حذف سنة مالية"""
        current_row = self.fiscal_years_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار سنة مالية للحذف")
            return
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   "هل تريد حذف السنة المالية المختارة؟\nهذا الإجراء لا يمكن التراجع عنه.")
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الحذف", "تم حذف السنة المالية بنجاح!")
    
    def close_fiscal_year(self):
        """إغلاق سنة مالية"""
        current_row = self.fiscal_years_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار سنة مالية للإغلاق")
            return
        
        reply = QMessageBox.question(self, "تأكيد الإغلاق", 
                                   "هل تريد إغلاق السنة المالية المختارة؟\nلن تتمكن من إضافة عمليات جديدة بعد الإغلاق.")
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الإغلاق", "تم إغلاق السنة المالية بنجاح!")
    
    def load_fiscal_year_data(self, row):
        """تحميل بيانات السنة المالية"""
        self.year_code.setText(self.fiscal_years_table.item(row, 0).text())
        self.year_name.setText(self.fiscal_years_table.item(row, 1).text())
        # تحميل باقي البيانات...
    
    def clear_form(self):
        """مسح النموذج"""
        self.year_code.clear()
        self.year_name.clear()
        self.is_active.setChecked(False)
        self.is_closed.setChecked(False)
        self.auto_close.setChecked(False)
        self.backup_before_close.setChecked(True)
        self.archive_data.setChecked(False)
    
    def close_screen(self):
        """إغلاق الشاشة"""
        if hasattr(self, 'main_window'):
            self.window().close()
        else:
            self.parent().close()
