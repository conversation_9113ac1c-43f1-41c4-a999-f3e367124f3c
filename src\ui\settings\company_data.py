"""
شاشة بيانات الشركة
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QLineEdit, QTextEdit, QComboBox, QCheckBox,
                               QGroupBox, QScrollArea, QPushButton, QFrame,
                               QMessageBox, QFileDialog)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon, QPixmap

from src.ui.styles.app_styles import AppStyles
from src.utils.arabic_support import ArabicSupport


class CompanyDataWidget(QWidget):
    """شاشة إدارة بيانات الشركة"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الشاشة
        title = QLabel("بيانات الشركة والمؤسسة")
        title.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                font-size: 18px; 
                color: #2c3e50; 
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # إضافة المجموعات
        self.create_basic_info_group(scroll_layout)
        self.create_contact_group(scroll_layout)
        self.create_legal_group(scroll_layout)
        self.create_financial_group(scroll_layout)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)
        
        # أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def create_basic_info_group(self, layout):
        """إنشاء مجموعة البيانات الأساسية"""
        basic_group = QGroupBox("البيانات الأساسية")
        basic_group.setStyleSheet(AppStyles.get_frame_style())
        basic_layout = QFormLayout(basic_group)
        
        self.company_name_ar = QLineEdit()
        self.company_name_ar.setPlaceholderText("اسم الشركة بالعربية")
        basic_layout.addRow("اسم الشركة (عربي):", self.company_name_ar)
        
        self.company_name_en = QLineEdit()
        self.company_name_en.setPlaceholderText("Company Name in English")
        basic_layout.addRow("اسم الشركة (إنجليزي):", self.company_name_en)
        
        self.company_type = QComboBox()
        self.company_type.addItems(["شركة محدودة", "مؤسسة فردية", "شركة مساهمة", "شركة تضامن"])
        basic_layout.addRow("نوع الشركة:", self.company_type)
        
        self.activity_description = QTextEdit()
        self.activity_description.setMaximumHeight(80)
        self.activity_description.setPlaceholderText("وصف نشاط الشركة")
        basic_layout.addRow("وصف النشاط:", self.activity_description)
        
        layout.addWidget(basic_group)
    
    def create_contact_group(self, layout):
        """إنشاء مجموعة بيانات الاتصال"""
        contact_group = QGroupBox("بيانات الاتصال")
        contact_group.setStyleSheet(AppStyles.get_frame_style())
        contact_layout = QFormLayout(contact_group)
        
        self.address = QTextEdit()
        self.address.setMaximumHeight(80)
        self.address.setPlaceholderText("العنوان الكامل")
        contact_layout.addRow("العنوان:", self.address)
        
        self.city = QLineEdit()
        self.city.setPlaceholderText("المدينة")
        contact_layout.addRow("المدينة:", self.city)
        
        self.country = QComboBox()
        self.country.addItems(["السعودية", "الإمارات", "مصر", "الأردن", "الكويت"])
        contact_layout.addRow("الدولة:", self.country)
        
        self.postal_code = QLineEdit()
        self.postal_code.setPlaceholderText("الرمز البريدي")
        contact_layout.addRow("الرمز البريدي:", self.postal_code)
        
        self.phone = QLineEdit()
        self.phone.setPlaceholderText("رقم الهاتف")
        contact_layout.addRow("الهاتف:", self.phone)
        
        self.email = QLineEdit()
        self.email.setPlaceholderText("البريد الإلكتروني")
        contact_layout.addRow("البريد الإلكتروني:", self.email)
        
        self.website = QLineEdit()
        self.website.setPlaceholderText("الموقع الإلكتروني")
        contact_layout.addRow("الموقع الإلكتروني:", self.website)
        
        layout.addWidget(contact_group)
    
    def create_legal_group(self, layout):
        """إنشاء مجموعة البيانات القانونية"""
        legal_group = QGroupBox("البيانات القانونية")
        legal_group.setStyleSheet(AppStyles.get_frame_style())
        legal_layout = QFormLayout(legal_group)
        
        self.commercial_register = QLineEdit()
        self.commercial_register.setPlaceholderText("رقم السجل التجاري")
        legal_layout.addRow("السجل التجاري:", self.commercial_register)
        
        self.tax_number = QLineEdit()
        self.tax_number.setPlaceholderText("الرقم الضريبي")
        legal_layout.addRow("الرقم الضريبي:", self.tax_number)
        
        self.license_number = QLineEdit()
        self.license_number.setPlaceholderText("رقم الترخيص")
        legal_layout.addRow("رقم الترخيص:", self.license_number)
        
        self.chamber_membership = QLineEdit()
        self.chamber_membership.setPlaceholderText("رقم عضوية الغرفة التجارية")
        legal_layout.addRow("عضوية الغرفة التجارية:", self.chamber_membership)
        
        layout.addWidget(legal_group)
    
    def create_financial_group(self, layout):
        """إنشاء مجموعة البيانات المالية"""
        financial_group = QGroupBox("البيانات المالية")
        financial_group.setStyleSheet(AppStyles.get_frame_style())
        financial_layout = QFormLayout(financial_group)
        
        self.bank_name = QLineEdit()
        self.bank_name.setPlaceholderText("اسم البنك")
        financial_layout.addRow("البنك الرئيسي:", self.bank_name)
        
        self.account_number = QLineEdit()
        self.account_number.setPlaceholderText("رقم الحساب")
        financial_layout.addRow("رقم الحساب:", self.account_number)
        
        self.iban = QLineEdit()
        self.iban.setPlaceholderText("رقم الآيبان")
        financial_layout.addRow("رقم الآيبان:", self.iban)
        
        self.swift_code = QLineEdit()
        self.swift_code.setPlaceholderText("رمز السويفت")
        financial_layout.addRow("رمز السويفت:", self.swift_code)
        
        layout.addWidget(financial_group)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر الحفظ
        save_btn = QPushButton("حفظ البيانات")
        save_btn.setIcon(QIcon("assets/icons/save.png"))
        save_btn.clicked.connect(self.save_company_data)
        save_btn.setStyleSheet(AppStyles.get_button_style("success"))
        buttons_layout.addWidget(save_btn)
        
        # زر طباعة البيانات
        print_btn = QPushButton("طباعة البيانات")
        print_btn.setIcon(QIcon("assets/icons/print.png"))
        print_btn.clicked.connect(self.print_company_data)
        print_btn.setStyleSheet(AppStyles.get_button_style("info"))
        buttons_layout.addWidget(print_btn)
        
        # زر تصدير البيانات
        export_btn = QPushButton("تصدير البيانات")
        export_btn.setIcon(QIcon("assets/icons/export.png"))
        export_btn.clicked.connect(self.export_company_data)
        export_btn.setStyleSheet(AppStyles.get_button_style("warning"))
        buttons_layout.addWidget(export_btn)
        
        buttons_layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setIcon(QIcon("assets/icons/close.png"))
        close_btn.clicked.connect(self.close_screen)
        close_btn.setStyleSheet(AppStyles.get_button_style("secondary"))
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)
    
    def save_company_data(self):
        """حفظ بيانات الشركة"""
        try:
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ بيانات الشركة بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
    
    def print_company_data(self):
        """طباعة بيانات الشركة"""
        QMessageBox.information(self, "طباعة", "سيتم طباعة بيانات الشركة...")
    
    def export_company_data(self):
        """تصدير بيانات الشركة"""
        file_path, _ = QFileDialog.getSaveFileName(self, "تصدير بيانات الشركة", 
                                                 "company_data.pdf", "PDF Files (*.pdf)")
        if file_path:
            QMessageBox.information(self, "تصدير", f"تم تصدير البيانات إلى: {file_path}")
    
    def close_screen(self):
        """إغلاق الشاشة"""
        if hasattr(self, 'main_window'):
            self.window().close()
        else:
            self.parent().close()
