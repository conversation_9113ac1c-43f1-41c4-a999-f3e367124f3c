"""
شاشة المتغيرات العامة للنظام
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QLineEdit, QSpinBox, QComboBox, QCheckBox,
                               QGroupBox, QScrollArea, QPushButton, QFrame,
                               QMessageBox, QFileDialog)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from src.utils.styles import AppStyles
from src.utils.arabic_support import ArabicSupport


class SystemVariablesWidget(QWidget):
    """شاشة إدارة المتغيرات العامة للنظام"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الشاشة
        title = QLabel("إعدادات المتغيرات العامة للبرنامج")
        title.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                font-size: 18px; 
                color: #2c3e50; 
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # إضافة المجموعات
        self.create_system_group(scroll_layout)
        self.create_database_group(scroll_layout)
        self.create_interface_group(scroll_layout)
        self.create_security_group(scroll_layout)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)
        
        # أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def create_system_group(self, layout):
        """إنشاء مجموعة إعدادات النظام"""
        system_group = QGroupBox("إعدادات النظام الأساسية")
        system_group.setStyleSheet(AppStyles.get_frame_style())
        system_layout = QFormLayout(system_group)
        
        self.company_name = QLineEdit()
        self.company_name.setPlaceholderText("اسم الشركة")
        self.company_name.setText("شركة الشحنات المتكاملة")
        system_layout.addRow("اسم الشركة:", self.company_name)
        
        self.system_version = QLineEdit()
        self.system_version.setText("1.0.0")
        self.system_version.setReadOnly(True)
        system_layout.addRow("إصدار النظام:", self.system_version)
        
        self.decimal_places = QSpinBox()
        self.decimal_places.setRange(0, 6)
        self.decimal_places.setValue(2)
        system_layout.addRow("عدد الخانات العشرية:", self.decimal_places)
        
        self.default_currency = QComboBox()
        self.default_currency.addItems(["ريال سعودي", "دولار أمريكي", "يورو", "جنيه مصري"])
        system_layout.addRow("العملة الافتراضية:", self.default_currency)
        
        layout.addWidget(system_group)
    
    def create_database_group(self, layout):
        """إنشاء مجموعة إعدادات قاعدة البيانات"""
        db_group = QGroupBox("إعدادات قاعدة البيانات")
        db_group.setStyleSheet(AppStyles.get_frame_style())
        db_layout = QFormLayout(db_group)
        
        self.db_type = QComboBox()
        self.db_type.addItems(["SQLite", "MySQL", "PostgreSQL"])
        db_layout.addRow("نوع قاعدة البيانات:", self.db_type)
        
        self.backup_enabled = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        self.backup_enabled.setChecked(True)
        db_layout.addRow("النسخ الاحتياطي:", self.backup_enabled)
        
        self.backup_interval = QSpinBox()
        self.backup_interval.setRange(1, 24)
        self.backup_interval.setValue(6)
        self.backup_interval.setSuffix(" ساعة")
        db_layout.addRow("فترة النسخ الاحتياطي:", self.backup_interval)
        
        self.max_backups = QSpinBox()
        self.max_backups.setRange(5, 100)
        self.max_backups.setValue(30)
        db_layout.addRow("عدد النسخ المحفوظة:", self.max_backups)
        
        layout.addWidget(db_group)
    
    def create_interface_group(self, layout):
        """إنشاء مجموعة إعدادات الواجهة"""
        ui_group = QGroupBox("إعدادات الواجهة")
        ui_group.setStyleSheet(AppStyles.get_frame_style())
        ui_layout = QFormLayout(ui_group)
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["العربية", "English"])
        ui_layout.addRow("اللغة:", self.language_combo)
        
        self.font_size = QSpinBox()
        self.font_size.setRange(8, 20)
        self.font_size.setValue(10)
        ui_layout.addRow("حجم الخط:", self.font_size)
        
        self.rtl_enabled = QCheckBox("تفعيل RTL")
        self.rtl_enabled.setChecked(True)
        ui_layout.addRow("اتجاه النص:", self.rtl_enabled)
        
        self.theme = QComboBox()
        self.theme.addItems(["الافتراضي", "داكن", "فاتح"])
        ui_layout.addRow("المظهر:", self.theme)
        
        layout.addWidget(ui_group)
    
    def create_security_group(self, layout):
        """إنشاء مجموعة إعدادات الأمان"""
        security_group = QGroupBox("إعدادات الأمان")
        security_group.setStyleSheet(AppStyles.get_frame_style())
        security_layout = QFormLayout(security_group)
        
        self.session_timeout = QSpinBox()
        self.session_timeout.setRange(5, 480)
        self.session_timeout.setValue(60)
        self.session_timeout.setSuffix(" دقيقة")
        security_layout.addRow("انتهاء الجلسة:", self.session_timeout)
        
        self.password_complexity = QCheckBox("تفعيل تعقيد كلمة المرور")
        self.password_complexity.setChecked(True)
        security_layout.addRow("تعقيد كلمة المرور:", self.password_complexity)
        
        self.audit_enabled = QCheckBox("تفعيل سجل العمليات")
        self.audit_enabled.setChecked(True)
        security_layout.addRow("سجل العمليات:", self.audit_enabled)
        
        layout.addWidget(security_group)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر الحفظ
        save_btn = QPushButton("حفظ الإعدادات")
        save_btn.setIcon(QIcon("assets/icons/save.png"))
        save_btn.clicked.connect(self.save_settings)
        save_btn.setStyleSheet(AppStyles.get_button_style("success"))
        buttons_layout.addWidget(save_btn)
        
        # زر الاستعادة
        restore_btn = QPushButton("استعادة الافتراضي")
        restore_btn.setIcon(QIcon("assets/icons/restore.png"))
        restore_btn.clicked.connect(self.restore_defaults)
        restore_btn.setStyleSheet(AppStyles.get_button_style("warning"))
        buttons_layout.addWidget(restore_btn)
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setIcon(QIcon("assets/icons/close.png"))
        close_btn.clicked.connect(self.close_screen)
        close_btn.setStyleSheet(AppStyles.get_button_style("secondary"))
        buttons_layout.addWidget(close_btn)
        
        buttons_layout.addStretch()
        layout.addWidget(buttons_frame)
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # هنا يتم حفظ الإعدادات في قاعدة البيانات
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ إعدادات المتغيرات العامة بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ الإعدادات: {str(e)}")
    
    def restore_defaults(self):
        """استعادة الإعدادات الافتراضية"""
        reply = QMessageBox.question(self, "تأكيد", 
                                   "هل تريد استعادة الإعدادات الافتراضية؟\nسيتم فقدان جميع التغييرات الحالية.")
        
        if reply == QMessageBox.Yes:
            # استعادة القيم الافتراضية
            self.company_name.setText("شركة الشحنات المتكاملة")
            self.decimal_places.setValue(2)
            self.default_currency.setCurrentIndex(0)
            self.backup_enabled.setChecked(True)
            self.backup_interval.setValue(6)
            self.max_backups.setValue(30)
            self.language_combo.setCurrentIndex(0)
            self.font_size.setValue(10)
            self.rtl_enabled.setChecked(True)
            self.theme.setCurrentIndex(0)
            self.session_timeout.setValue(60)
            self.password_complexity.setChecked(True)
            self.audit_enabled.setChecked(True)
            
            QMessageBox.information(self, "تمت الاستعادة", "تم استعادة الإعدادات الافتراضية بنجاح!")
    
    def close_screen(self):
        """إغلاق الشاشة"""
        if hasattr(self, 'main_window'):
            # إذا كانت النافذة مفتوحة في وضع ملء الشاشة
            self.window().close()
        else:
            # إذا كانت النافذة مدمجة في النافذة الرئيسية
            self.parent().close()
