"""
شاشة إدارة مجموعات الأصناف
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QLineEdit, QComboBox, QCheckBox, QTextEdit,
                               QGroupBox, QPushButton, QFrame, QTreeWidget, QTreeWidgetItem,
                               QMessageBox, QTableWidget, QTableWidgetItem)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from src.utils.styles import AppStyles
from src.utils.arabic_support import ArabicSupport


class ItemGroupsWidget(QWidget):
    """شاشة إدارة مجموعات الأصناف"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الشاشة
        title = QLabel("إدارة مجموعات الأصناف")
        title.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                font-size: 18px; 
                color: #2c3e50; 
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()
        
        # الجانب الأيسر - نموذج البيانات
        self.create_form_section(content_layout)
        
        # الجانب الأيمن - شجرة المجموعات
        self.create_tree_section(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def create_form_section(self, layout):
        """إنشاء قسم النموذج"""
        form_frame = QFrame()
        form_frame.setMaximumWidth(400)
        form_layout = QVBoxLayout(form_frame)
        
        # مجموعة بيانات المجموعة
        group_group = QGroupBox("بيانات المجموعة")
        group_group.setStyleSheet(AppStyles.get_frame_style())
        group_layout = QFormLayout(group_group)
        
        self.group_code = QLineEdit()
        self.group_code.setPlaceholderText("رمز المجموعة")
        group_layout.addRow("رمز المجموعة:", self.group_code)
        
        self.group_name_ar = QLineEdit()
        self.group_name_ar.setPlaceholderText("اسم المجموعة بالعربية")
        group_layout.addRow("الاسم (عربي):", self.group_name_ar)
        
        self.group_name_en = QLineEdit()
        self.group_name_en.setPlaceholderText("Group Name in English")
        group_layout.addRow("الاسم (إنجليزي):", self.group_name_en)
        
        self.parent_group = QComboBox()
        self.parent_group.addItems(["-- مجموعة رئيسية --", "إلكترونيات", "ملابس", "أغذية", "مواد بناء"])
        group_layout.addRow("المجموعة الأب:", self.parent_group)
        
        self.group_description = QTextEdit()
        self.group_description.setMaximumHeight(80)
        self.group_description.setPlaceholderText("وصف المجموعة")
        group_layout.addRow("الوصف:", self.group_description)
        
        self.is_active = QCheckBox("مجموعة نشطة")
        self.is_active.setChecked(True)
        group_layout.addRow("الحالة:", self.is_active)
        
        form_layout.addWidget(group_group)
        
        # مجموعة الإعدادات
        settings_group = QGroupBox("إعدادات المجموعة")
        settings_group.setStyleSheet(AppStyles.get_frame_style())
        settings_layout = QFormLayout(settings_group)
        
        self.allow_negative_stock = QCheckBox("السماح بالمخزون السالب")
        settings_layout.addRow("المخزون السالب:", self.allow_negative_stock)
        
        self.track_expiry = QCheckBox("تتبع تاريخ الانتهاء")
        settings_layout.addRow("تتبع الانتهاء:", self.track_expiry)
        
        self.require_serial = QCheckBox("يتطلب رقم تسلسلي")
        settings_layout.addRow("الرقم التسلسلي:", self.require_serial)
        
        self.default_unit = QComboBox()
        self.default_unit.addItems(["قطعة", "كيلوجرام", "متر", "لتر"])
        settings_layout.addRow("الوحدة الافتراضية:", self.default_unit)
        
        form_layout.addWidget(settings_group)
        form_layout.addStretch()
        
        layout.addWidget(form_frame)
    
    def create_tree_section(self, layout):
        """إنشاء قسم شجرة المجموعات"""
        tree_frame = QFrame()
        tree_layout = QVBoxLayout(tree_frame)
        
        # عنوان الشجرة
        tree_title = QLabel("هيكل مجموعات الأصناف")
        tree_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        tree_layout.addWidget(tree_title)
        
        # شجرة المجموعات
        self.groups_tree = QTreeWidget()
        self.groups_tree.setHeaderLabels(["اسم المجموعة", "الرمز", "عدد الأصناف", "الحالة"])
        
        # إضافة البيانات التجريبية
        self.setup_groups_tree()
        
        tree_layout.addWidget(self.groups_tree)
        
        # إحصائيات المجموعات
        stats_frame = QFrame()
        stats_layout = QHBoxLayout(stats_frame)
        
        total_groups = QLabel("إجمالي المجموعات: 12")
        total_groups.setStyleSheet("font-weight: bold; color: #3498db;")
        stats_layout.addWidget(total_groups)
        
        active_groups = QLabel("المجموعات النشطة: 10")
        active_groups.setStyleSheet("font-weight: bold; color: #27ae60;")
        stats_layout.addWidget(active_groups)
        
        total_items = QLabel("إجمالي الأصناف: 156")
        total_items.setStyleSheet("font-weight: bold; color: #e74c3c;")
        stats_layout.addWidget(total_items)
        
        stats_layout.addStretch()
        tree_layout.addWidget(stats_frame)
        
        layout.addWidget(tree_frame)
    
    def setup_groups_tree(self):
        """إعداد شجرة المجموعات"""
        # المجموعات الرئيسية
        electronics = QTreeWidgetItem(self.groups_tree, ["إلكترونيات", "ELEC", "45", "نشطة"])
        electronics.setExpanded(True)
        
        # مجموعات فرعية للإلكترونيات
        QTreeWidgetItem(electronics, ["هواتف ذكية", "PHONE", "15", "نشطة"])
        QTreeWidgetItem(electronics, ["حاسوب محمول", "LAPTOP", "12", "نشطة"])
        QTreeWidgetItem(electronics, ["أجهزة منزلية", "HOME", "18", "نشطة"])
        
        # مجموعة الملابس
        clothing = QTreeWidgetItem(self.groups_tree, ["ملابس", "CLOTH", "38", "نشطة"])
        clothing.setExpanded(True)
        
        QTreeWidgetItem(clothing, ["ملابس رجالية", "MEN", "20", "نشطة"])
        QTreeWidgetItem(clothing, ["ملابس نسائية", "WOMEN", "18", "نشطة"])
        
        # مجموعة الأغذية
        food = QTreeWidgetItem(self.groups_tree, ["أغذية", "FOOD", "73", "نشطة"])
        food.setExpanded(True)
        
        QTreeWidgetItem(food, ["خضروات وفواكه", "VEGE", "25", "نشطة"])
        QTreeWidgetItem(food, ["منتجات ألبان", "DAIRY", "18", "نشطة"])
        QTreeWidgetItem(food, ["حبوب ومعلبات", "GRAIN", "30", "نشطة"])
        
        # تلوين العناصر حسب الحالة
        for i in range(self.groups_tree.topLevelItemCount()):
            item = self.groups_tree.topLevelItem(i)
            if item.text(3) == "نشطة":
                item.setBackground(0, AppStyles.get_color("success_light"))
            else:
                item.setBackground(0, AppStyles.get_color("secondary_light"))
            
            # تلوين العناصر الفرعية
            for j in range(item.childCount()):
                child = item.child(j)
                if child.text(3) == "نشطة":
                    child.setBackground(0, AppStyles.get_color("info_light"))
                else:
                    child.setBackground(0, AppStyles.get_color("secondary_light"))
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر إضافة
        add_btn = QPushButton("إضافة مجموعة")
        add_btn.setIcon(QIcon("assets/icons/add.png"))
        add_btn.clicked.connect(self.add_group)
        add_btn.setStyleSheet(AppStyles.get_button_style("primary"))
        buttons_layout.addWidget(add_btn)
        
        # زر تعديل
        edit_btn = QPushButton("تعديل")
        edit_btn.setIcon(QIcon("assets/icons/edit.png"))
        edit_btn.clicked.connect(self.edit_group)
        edit_btn.setStyleSheet(AppStyles.get_button_style("info"))
        buttons_layout.addWidget(edit_btn)
        
        # زر حفظ
        save_btn = QPushButton("حفظ")
        save_btn.setIcon(QIcon("assets/icons/save.png"))
        save_btn.clicked.connect(self.save_group)
        save_btn.setStyleSheet(AppStyles.get_button_style("success"))
        buttons_layout.addWidget(save_btn)
        
        # زر حذف
        delete_btn = QPushButton("حذف")
        delete_btn.setIcon(QIcon("assets/icons/delete.png"))
        delete_btn.clicked.connect(self.delete_group)
        delete_btn.setStyleSheet(AppStyles.get_button_style("danger"))
        buttons_layout.addWidget(delete_btn)
        
        # زر نقل المجموعة
        move_btn = QPushButton("نقل المجموعة")
        move_btn.setIcon(QIcon("assets/icons/move.png"))
        move_btn.clicked.connect(self.move_group)
        move_btn.setStyleSheet(AppStyles.get_button_style("warning"))
        buttons_layout.addWidget(move_btn)
        
        buttons_layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setIcon(QIcon("assets/icons/close.png"))
        close_btn.clicked.connect(self.close_screen)
        close_btn.setStyleSheet(AppStyles.get_button_style("secondary"))
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)
    
    def add_group(self):
        """إضافة مجموعة جديدة"""
        if not self.group_code.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز المجموعة")
            return
        
        if not self.group_name_ar.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المجموعة")
            return
        
        QMessageBox.information(self, "نجح", "تم إضافة المجموعة بنجاح!")
        self.clear_form()
    
    def edit_group(self):
        """تعديل مجموعة"""
        current_item = self.groups_tree.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مجموعة للتعديل")
            return
        
        self.load_group_data(current_item)
    
    def save_group(self):
        """حفظ المجموعة"""
        try:
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ بيانات المجموعة بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
    
    def delete_group(self):
        """حذف مجموعة"""
        current_item = self.groups_tree.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مجموعة للحذف")
            return
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   "هل تريد حذف المجموعة المختارة؟\n"
                                   "سيتم حذف جميع المجموعات الفرعية أيضاً.")
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الحذف", "تم حذف المجموعة بنجاح!")
    
    def move_group(self):
        """نقل مجموعة"""
        current_item = self.groups_tree.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مجموعة للنقل")
            return
        
        QMessageBox.information(self, "نقل المجموعة", "سيتم تطبيق هذه الميزة قريباً...")
    
    def load_group_data(self, item):
        """تحميل بيانات المجموعة"""
        self.group_name_ar.setText(item.text(0))
        self.group_code.setText(item.text(1))
    
    def clear_form(self):
        """مسح النموذج"""
        self.group_code.clear()
        self.group_name_ar.clear()
        self.group_name_en.clear()
        self.group_description.clear()
        self.is_active.setChecked(True)
        self.allow_negative_stock.setChecked(False)
        self.track_expiry.setChecked(False)
        self.require_serial.setChecked(False)
    
    def close_screen(self):
        """إغلاق الشاشة"""
        if hasattr(self, 'main_window'):
            self.window().close()
        else:
            self.parent().close()
