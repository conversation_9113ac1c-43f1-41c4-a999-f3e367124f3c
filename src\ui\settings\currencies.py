"""
شاشة إدارة العملات
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QLineEdit, QDoubleSpinBox, QComboBox, QCheckBox,
                               QGroupBox, QScrollArea, QPushButton, QFrame,
                               QMessageBox, QTableWidget, QTableWidgetItem,
                               QHeaderView, QDateEdit)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QIcon

from src.utils.styles import AppStyles
from src.utils.arabic_support import ArabicSupport


class CurrenciesWidget(QWidget):
    """شاشة إدارة العملات"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الشاشة
        title = QLabel("إدارة العملات وأسعار الصرف")
        title.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                font-size: 18px; 
                color: #2c3e50; 
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()
        
        # الجانب الأيسر - نموذج البيانات
        self.create_form_section(content_layout)
        
        # الجانب الأيمن - جدول العملات
        self.create_table_section(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def create_form_section(self, layout):
        """إنشاء قسم النموذج"""
        form_frame = QFrame()
        form_frame.setMaximumWidth(400)
        form_layout = QVBoxLayout(form_frame)
        
        # مجموعة بيانات العملة
        currency_group = QGroupBox("بيانات العملة")
        currency_group.setStyleSheet(AppStyles.get_frame_style())
        currency_layout = QFormLayout(currency_group)
        
        self.currency_code = QLineEdit()
        self.currency_code.setPlaceholderText("رمز العملة (مثل: USD)")
        self.currency_code.setMaxLength(3)
        currency_layout.addRow("رمز العملة:", self.currency_code)
        
        self.currency_name = QLineEdit()
        self.currency_name.setPlaceholderText("اسم العملة")
        currency_layout.addRow("اسم العملة:", self.currency_name)
        
        self.currency_symbol = QLineEdit()
        self.currency_symbol.setPlaceholderText("رمز العملة ($)")
        self.currency_symbol.setMaxLength(5)
        currency_layout.addRow("رمز العملة:", self.currency_symbol)
        
        self.is_base_currency = QCheckBox("عملة أساسية")
        currency_layout.addRow("النوع:", self.is_base_currency)
        
        self.is_active = QCheckBox("عملة نشطة")
        self.is_active.setChecked(True)
        currency_layout.addRow("الحالة:", self.is_active)
        
        form_layout.addWidget(currency_group)
        
        # مجموعة أسعار الصرف
        exchange_group = QGroupBox("أسعار الصرف")
        exchange_group.setStyleSheet(AppStyles.get_frame_style())
        exchange_layout = QFormLayout(exchange_group)
        
        self.exchange_rate = QDoubleSpinBox()
        self.exchange_rate.setRange(0.0001, 999999.9999)
        self.exchange_rate.setDecimals(4)
        self.exchange_rate.setValue(1.0000)
        exchange_layout.addRow("سعر الصرف:", self.exchange_rate)
        
        self.rate_date = QDateEdit()
        self.rate_date.setDate(QDate.currentDate())
        self.rate_date.setCalendarPopup(True)
        exchange_layout.addRow("تاريخ السعر:", self.rate_date)
        
        self.auto_update = QCheckBox("تحديث تلقائي")
        exchange_layout.addRow("التحديث:", self.auto_update)
        
        self.update_source = QComboBox()
        self.update_source.addItems(["يدوي", "البنك المركزي", "Yahoo Finance", "XE.com"])
        exchange_layout.addRow("مصدر التحديث:", self.update_source)
        
        form_layout.addWidget(exchange_group)
        
        # مجموعة الإعدادات الإضافية
        settings_group = QGroupBox("إعدادات إضافية")
        settings_group.setStyleSheet(AppStyles.get_frame_style())
        settings_layout = QFormLayout(settings_group)
        
        self.decimal_places = QComboBox()
        self.decimal_places.addItems(["0", "1", "2", "3", "4"])
        self.decimal_places.setCurrentText("2")
        settings_layout.addRow("الخانات العشرية:", self.decimal_places)
        
        self.rounding_method = QComboBox()
        self.rounding_method.addItems(["تقريب عادي", "تقريب لأعلى", "تقريب لأسفل"])
        settings_layout.addRow("طريقة التقريب:", self.rounding_method)
        
        form_layout.addWidget(settings_group)
        form_layout.addStretch()
        
        layout.addWidget(form_frame)
    
    def create_table_section(self, layout):
        """إنشاء قسم الجدول"""
        table_frame = QFrame()
        table_layout = QVBoxLayout(table_frame)
        
        # عنوان الجدول
        table_title = QLabel("العملات المسجلة")
        table_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        table_layout.addWidget(table_title)
        
        # الجدول
        self.currencies_table = QTableWidget()
        self.currencies_table.setColumnCount(7)
        self.currencies_table.setHorizontalHeaderLabels([
            "الرمز", "اسم العملة", "الرمز", "سعر الصرف", "تاريخ التحديث", "النوع", "الحالة"
        ])
        
        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.currencies_table)
        
        # إضافة بيانات تجريبية
        self.currencies_table.setRowCount(6)
        currencies_data = [
            ["SAR", "ريال سعودي", "ر.س", "1.0000", "2024-01-15", "أساسية", "نشطة"],
            ["USD", "دولار أمريكي", "$", "3.7500", "2024-01-15", "فرعية", "نشطة"],
            ["EUR", "يورو", "€", "4.1200", "2024-01-15", "فرعية", "نشطة"],
            ["GBP", "جنيه إسترليني", "£", "4.7800", "2024-01-15", "فرعية", "نشطة"],
            ["EGP", "جنيه مصري", "ج.م", "0.1200", "2024-01-15", "فرعية", "نشطة"],
            ["AED", "درهم إماراتي", "د.إ", "1.0200", "2024-01-15", "فرعية", "غير نشطة"]
        ]
        
        for row, data in enumerate(currencies_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 5:  # عمود النوع
                    if value == "أساسية":
                        item.setBackground(AppStyles.get_color("primary_light"))
                    else:
                        item.setBackground(AppStyles.get_color("info_light"))
                elif col == 6:  # عمود الحالة
                    if value == "نشطة":
                        item.setBackground(AppStyles.get_color("success_light"))
                    else:
                        item.setBackground(AppStyles.get_color("secondary_light"))
                
                self.currencies_table.setItem(row, col, item)
        
        table_layout.addWidget(self.currencies_table)
        
        # إحصائيات العملات
        stats_frame = QFrame()
        stats_layout = QHBoxLayout(stats_frame)
        
        total_currencies = QLabel("إجمالي العملات: 6")
        total_currencies.setStyleSheet("font-weight: bold; color: #3498db;")
        stats_layout.addWidget(total_currencies)
        
        active_currencies = QLabel("العملات النشطة: 5")
        active_currencies.setStyleSheet("font-weight: bold; color: #27ae60;")
        stats_layout.addWidget(active_currencies)
        
        base_currency = QLabel("العملة الأساسية: ريال سعودي")
        base_currency.setStyleSheet("font-weight: bold; color: #e74c3c;")
        stats_layout.addWidget(base_currency)
        
        stats_layout.addStretch()
        table_layout.addWidget(stats_frame)
        
        layout.addWidget(table_frame)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر إضافة
        add_btn = QPushButton("إضافة عملة")
        add_btn.setIcon(QIcon("assets/icons/add.png"))
        add_btn.clicked.connect(self.add_currency)
        add_btn.setStyleSheet(AppStyles.get_button_style("primary"))
        buttons_layout.addWidget(add_btn)
        
        # زر تعديل
        edit_btn = QPushButton("تعديل")
        edit_btn.setIcon(QIcon("assets/icons/edit.png"))
        edit_btn.clicked.connect(self.edit_currency)
        edit_btn.setStyleSheet(AppStyles.get_button_style("info"))
        buttons_layout.addWidget(edit_btn)
        
        # زر حفظ
        save_btn = QPushButton("حفظ")
        save_btn.setIcon(QIcon("assets/icons/save.png"))
        save_btn.clicked.connect(self.save_currency)
        save_btn.setStyleSheet(AppStyles.get_button_style("success"))
        buttons_layout.addWidget(save_btn)
        
        # زر حذف
        delete_btn = QPushButton("حذف")
        delete_btn.setIcon(QIcon("assets/icons/delete.png"))
        delete_btn.clicked.connect(self.delete_currency)
        delete_btn.setStyleSheet(AppStyles.get_button_style("danger"))
        buttons_layout.addWidget(delete_btn)
        
        # زر تحديث الأسعار
        update_rates_btn = QPushButton("تحديث الأسعار")
        update_rates_btn.setIcon(QIcon("assets/icons/refresh.png"))
        update_rates_btn.clicked.connect(self.update_exchange_rates)
        update_rates_btn.setStyleSheet(AppStyles.get_button_style("warning"))
        buttons_layout.addWidget(update_rates_btn)
        
        buttons_layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setIcon(QIcon("assets/icons/close.png"))
        close_btn.clicked.connect(self.close_screen)
        close_btn.setStyleSheet(AppStyles.get_button_style("secondary"))
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)
    
    def add_currency(self):
        """إضافة عملة جديدة"""
        if not self.currency_code.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز العملة")
            return
        
        if not self.currency_name.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم العملة")
            return
        
        QMessageBox.information(self, "نجح", "تم إضافة العملة بنجاح!")
        self.clear_form()
    
    def edit_currency(self):
        """تعديل عملة"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عملة للتعديل")
            return
        
        self.load_currency_data(current_row)
    
    def save_currency(self):
        """حفظ العملة"""
        try:
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ بيانات العملة بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
    
    def delete_currency(self):
        """حذف عملة"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عملة للحذف")
            return
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   "هل تريد حذف العملة المختارة؟")
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الحذف", "تم حذف العملة بنجاح!")
    
    def update_exchange_rates(self):
        """تحديث أسعار الصرف"""
        QMessageBox.information(self, "تحديث الأسعار", "تم تحديث أسعار الصرف بنجاح!")
    
    def load_currency_data(self, row):
        """تحميل بيانات العملة"""
        self.currency_code.setText(self.currencies_table.item(row, 0).text())
        self.currency_name.setText(self.currencies_table.item(row, 1).text())
        self.currency_symbol.setText(self.currencies_table.item(row, 2).text())
        # تحميل باقي البيانات...
    
    def clear_form(self):
        """مسح النموذج"""
        self.currency_code.clear()
        self.currency_name.clear()
        self.currency_symbol.clear()
        self.is_base_currency.setChecked(False)
        self.is_active.setChecked(True)
        self.exchange_rate.setValue(1.0000)
        self.auto_update.setChecked(False)
    
    def close_screen(self):
        """إغلاق الشاشة"""
        if hasattr(self, 'main_window'):
            self.window().close()
        else:
            self.parent().close()
