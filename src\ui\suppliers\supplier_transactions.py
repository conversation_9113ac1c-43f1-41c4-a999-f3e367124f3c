"""
شاشة معاملات الموردين
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QLineEdit, QComboBox, QDateEdit, QDoubleSpinBox,
                               QGroupBox, QPushButton, QFrame, QTabWidget,
                               QMessageBox, QTableWidget, QTableWidgetItem, QTextEdit)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QIcon

from src.utils.styles import AppStyles
from src.utils.arabic_support import ArabicSupport


class SupplierTransactionsWidget(QWidget):
    """شاشة معاملات الموردين"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الشاشة
        title = QLabel("معاملات الموردين")
        title.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                font-size: 18px; 
                color: #2c3e50; 
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()
        
        # الجانب الأيسر - نموذج المعاملة
        self.create_form_section(content_layout)
        
        # الجانب الأيمن - جدول المعاملات
        self.create_table_section(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def create_form_section(self, layout):
        """إنشاء قسم النموذج"""
        form_frame = QFrame()
        form_frame.setMaximumWidth(450)
        form_layout = QVBoxLayout(form_frame)
        
        # مجموعة بيانات المعاملة
        transaction_group = QGroupBox("بيانات المعاملة")
        transaction_group.setStyleSheet(AppStyles.get_frame_style())
        transaction_layout = QFormLayout(transaction_group)
        
        self.transaction_number = QLineEdit()
        self.transaction_number.setPlaceholderText("رقم المعاملة")
        transaction_layout.addRow("رقم المعاملة:", self.transaction_number)
        
        self.supplier = QComboBox()
        self.supplier.addItems([
            "شركة التقنية المتقدمة", "مؤسسة الأزياء العصرية", 
            "مصنع الأغذية الطازجة", "شركة مواد البناء"
        ])
        transaction_layout.addRow("المورد:", self.supplier)
        
        self.transaction_type = QComboBox()
        self.transaction_type.addItems(["فاتورة شراء", "مردود مشتريات", "دفعة", "خصم"])
        transaction_layout.addRow("نوع المعاملة:", self.transaction_type)
        
        self.transaction_date = QDateEdit()
        self.transaction_date.setDate(QDate.currentDate())
        self.transaction_date.setCalendarPopup(True)
        transaction_layout.addRow("تاريخ المعاملة:", self.transaction_date)
        
        self.due_date = QDateEdit()
        self.due_date.setDate(QDate.currentDate().addDays(30))
        self.due_date.setCalendarPopup(True)
        transaction_layout.addRow("تاريخ الاستحقاق:", self.due_date)
        
        form_layout.addWidget(transaction_group)
        
        # مجموعة المبالغ
        amounts_group = QGroupBox("المبالغ")
        amounts_group.setStyleSheet(AppStyles.get_frame_style())
        amounts_layout = QFormLayout(amounts_group)
        
        self.amount = QDoubleSpinBox()
        self.amount.setMaximum(999999.99)
        self.amount.setSuffix(" ريال")
        amounts_layout.addRow("المبلغ:", self.amount)
        
        self.tax_amount = QDoubleSpinBox()
        self.tax_amount.setMaximum(999999.99)
        self.tax_amount.setSuffix(" ريال")
        amounts_layout.addRow("مبلغ الضريبة:", self.tax_amount)
        
        self.discount_amount = QDoubleSpinBox()
        self.discount_amount.setMaximum(999999.99)
        self.discount_amount.setSuffix(" ريال")
        amounts_layout.addRow("مبلغ الخصم:", self.discount_amount)
        
        self.total_amount = QDoubleSpinBox()
        self.total_amount.setMaximum(999999.99)
        self.total_amount.setSuffix(" ريال")
        self.total_amount.setReadOnly(True)
        amounts_layout.addRow("المبلغ الإجمالي:", self.total_amount)
        
        form_layout.addWidget(amounts_group)
        
        # مجموعة الدفع
        payment_group = QGroupBox("بيانات الدفع")
        payment_group.setStyleSheet(AppStyles.get_frame_style())
        payment_layout = QFormLayout(payment_group)
        
        self.payment_method = QComboBox()
        self.payment_method.addItems(["نقداً", "شيك", "تحويل بنكي", "بطاقة ائتمان"])
        payment_layout.addRow("طريقة الدفع:", self.payment_method)
        
        self.payment_status = QComboBox()
        self.payment_status.addItems(["مدفوع", "غير مدفوع", "مدفوع جزئياً", "متأخر"])
        payment_layout.addRow("حالة الدفع:", self.payment_status)
        
        self.paid_amount = QDoubleSpinBox()
        self.paid_amount.setMaximum(999999.99)
        self.paid_amount.setSuffix(" ريال")
        payment_layout.addRow("المبلغ المدفوع:", self.paid_amount)
        
        self.remaining_amount = QDoubleSpinBox()
        self.remaining_amount.setMaximum(999999.99)
        self.remaining_amount.setSuffix(" ريال")
        self.remaining_amount.setReadOnly(True)
        payment_layout.addRow("المبلغ المتبقي:", self.remaining_amount)
        
        form_layout.addWidget(payment_group)
        
        # مجموعة الملاحظات
        notes_group = QGroupBox("الملاحظات")
        notes_group.setStyleSheet(AppStyles.get_frame_style())
        notes_layout = QVBoxLayout(notes_group)
        
        self.notes = QTextEdit()
        self.notes.setMaximumHeight(80)
        self.notes.setPlaceholderText("ملاحظات حول المعاملة")
        notes_layout.addWidget(self.notes)
        
        form_layout.addWidget(notes_group)
        form_layout.addStretch()
        
        layout.addWidget(form_frame)
    
    def create_table_section(self, layout):
        """إنشاء قسم الجدول"""
        table_frame = QFrame()
        table_layout = QVBoxLayout(table_frame)
        
        # شريط البحث والفلترة
        search_frame = QFrame()
        search_layout = QHBoxLayout(search_frame)
        
        search_label = QLabel("البحث:")
        search_layout.addWidget(search_label)
        
        self.search_field = QLineEdit()
        self.search_field.setPlaceholderText("البحث في المعاملات...")
        search_layout.addWidget(self.search_field)
        
        filter_label = QLabel("الفلترة:")
        search_layout.addWidget(filter_label)
        
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["جميع المعاملات", "فواتير الشراء", "المردودات", "الدفعات"])
        search_layout.addWidget(self.filter_combo)
        
        search_btn = QPushButton("بحث")
        search_btn.setStyleSheet(AppStyles.get_button_style("info"))
        search_layout.addWidget(search_btn)
        
        table_layout.addWidget(search_frame)
        
        # عنوان الجدول
        table_title = QLabel("سجل المعاملات")
        table_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        table_layout.addWidget(table_title)
        
        # الجدول
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(9)
        self.transactions_table.setHorizontalHeaderLabels([
            "رقم المعاملة", "المورد", "النوع", "التاريخ", "المبلغ", "المدفوع", 
            "المتبقي", "حالة الدفع", "الاستحقاق"
        ])
        
        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.transactions_table)
        
        # إضافة بيانات تجريبية
        self.transactions_table.setRowCount(12)
        transactions_data = [
            ["TXN001", "شركة التقنية المتقدمة", "فاتورة شراء", "2024-01-15", "15000.00", "15000.00", "0.00", "مدفوع", "2024-02-15"],
            ["TXN002", "مؤسسة الأزياء العصرية", "فاتورة شراء", "2024-01-20", "8500.00", "5000.00", "3500.00", "مدفوع جزئياً", "2024-02-20"],
            ["TXN003", "مصنع الأغذية الطازجة", "فاتورة شراء", "2024-01-25", "12000.00", "0.00", "12000.00", "غير مدفوع", "2024-02-25"],
            ["TXN004", "شركة مواد البناء", "مردود مشتريات", "2024-01-28", "2500.00", "2500.00", "0.00", "مدفوع", "2024-01-28"],
            ["TXN005", "شركة التقنية المتقدمة", "دفعة", "2024-02-01", "7500.00", "7500.00", "0.00", "مدفوع", "2024-02-01"],
            ["TXN006", "مؤسسة الأزياء العصرية", "فاتورة شراء", "2024-02-05", "6800.00", "6800.00", "0.00", "مدفوع", "2024-03-05"],
            ["TXN007", "مصنع الأغذية الطازجة", "دفعة", "2024-02-10", "5000.00", "5000.00", "0.00", "مدفوع", "2024-02-10"],
            ["TXN008", "شركة مواد البناء", "فاتورة شراء", "2024-02-12", "18000.00", "10000.00", "8000.00", "مدفوع جزئياً", "2024-03-12"],
            ["TXN009", "شركة التقنية المتقدمة", "فاتورة شراء", "2024-02-15", "22000.00", "0.00", "22000.00", "متأخر", "2024-03-15"],
            ["TXN010", "مؤسسة الأزياء العصرية", "خصم", "2024-02-18", "500.00", "500.00", "0.00", "مدفوع", "2024-02-18"],
            ["TXN011", "مصنع الأغذية الطازجة", "فاتورة شراء", "2024-02-20", "9500.00", "9500.00", "0.00", "مدفوع", "2024-03-20"],
            ["TXN012", "شركة مواد البناء", "مردود مشتريات", "2024-02-22", "1800.00", "1800.00", "0.00", "مدفوع", "2024-02-22"]
        ]
        
        for row, data in enumerate(transactions_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 2:  # عمود النوع
                    if value == "فاتورة شراء":
                        item.setBackground(AppStyles.get_color("primary_light"))
                    elif value == "مردود مشتريات":
                        item.setBackground(AppStyles.get_color("warning_light"))
                    elif value == "دفعة":
                        item.setBackground(AppStyles.get_color("success_light"))
                    else:
                        item.setBackground(AppStyles.get_color("info_light"))
                elif col == 7:  # عمود حالة الدفع
                    if value == "مدفوع":
                        item.setBackground(AppStyles.get_color("success_light"))
                    elif value == "غير مدفوع":
                        item.setBackground(AppStyles.get_color("danger_light"))
                    elif value == "مدفوع جزئياً":
                        item.setBackground(AppStyles.get_color("warning_light"))
                    else:  # متأخر
                        item.setBackground(AppStyles.get_color("danger"))
                
                self.transactions_table.setItem(row, col, item)
        
        table_layout.addWidget(self.transactions_table)
        layout.addWidget(table_frame)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر إضافة معاملة
        add_btn = QPushButton("إضافة معاملة")
        add_btn.setIcon(QIcon("assets/icons/add.png"))
        add_btn.clicked.connect(self.add_transaction)
        add_btn.setStyleSheet(AppStyles.get_button_style("primary"))
        buttons_layout.addWidget(add_btn)
        
        # زر تعديل
        edit_btn = QPushButton("تعديل")
        edit_btn.setIcon(QIcon("assets/icons/edit.png"))
        edit_btn.clicked.connect(self.edit_transaction)
        edit_btn.setStyleSheet(AppStyles.get_button_style("info"))
        buttons_layout.addWidget(edit_btn)
        
        # زر حفظ
        save_btn = QPushButton("حفظ")
        save_btn.setIcon(QIcon("assets/icons/save.png"))
        save_btn.clicked.connect(self.save_transaction)
        save_btn.setStyleSheet(AppStyles.get_button_style("success"))
        buttons_layout.addWidget(save_btn)
        
        # زر حذف
        delete_btn = QPushButton("حذف")
        delete_btn.setIcon(QIcon("assets/icons/delete.png"))
        delete_btn.clicked.connect(self.delete_transaction)
        delete_btn.setStyleSheet(AppStyles.get_button_style("danger"))
        buttons_layout.addWidget(delete_btn)
        
        # زر طباعة
        print_btn = QPushButton("طباعة")
        print_btn.setIcon(QIcon("assets/icons/print.png"))
        print_btn.clicked.connect(self.print_transaction)
        print_btn.setStyleSheet(AppStyles.get_button_style("warning"))
        buttons_layout.addWidget(print_btn)
        
        buttons_layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setIcon(QIcon("assets/icons/close.png"))
        close_btn.clicked.connect(self.close_screen)
        close_btn.setStyleSheet(AppStyles.get_button_style("secondary"))
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)
    
    def add_transaction(self):
        """إضافة معاملة جديدة"""
        if not self.transaction_number.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم المعاملة")
            return
        
        if self.amount.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
            return
        
        QMessageBox.information(self, "نجح", "تم إضافة المعاملة بنجاح!")
        self.clear_form()
    
    def edit_transaction(self):
        """تعديل معاملة"""
        current_row = self.transactions_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار معاملة للتعديل")
            return
        
        self.load_transaction_data(current_row)
    
    def save_transaction(self):
        """حفظ المعاملة"""
        try:
            # حساب المبلغ الإجمالي
            total = self.amount.value() + self.tax_amount.value() - self.discount_amount.value()
            self.total_amount.setValue(total)
            
            # حساب المبلغ المتبقي
            remaining = self.total_amount.value() - self.paid_amount.value()
            self.remaining_amount.setValue(remaining)
            
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ بيانات المعاملة بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
    
    def delete_transaction(self):
        """حذف معاملة"""
        current_row = self.transactions_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار معاملة للحذف")
            return
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   "هل تريد حذف المعاملة المختارة؟")
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الحذف", "تم حذف المعاملة بنجاح!")
    
    def print_transaction(self):
        """طباعة المعاملة"""
        current_row = self.transactions_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار معاملة للطباعة")
            return
        
        QMessageBox.information(self, "طباعة", "تم إرسال المعاملة للطباعة!")
    
    def load_transaction_data(self, row):
        """تحميل بيانات المعاملة"""
        self.transaction_number.setText(self.transactions_table.item(row, 0).text())
        self.amount.setValue(float(self.transactions_table.item(row, 4).text()))
        self.paid_amount.setValue(float(self.transactions_table.item(row, 5).text()))
        self.remaining_amount.setValue(float(self.transactions_table.item(row, 6).text()))
    
    def clear_form(self):
        """مسح النموذج"""
        self.transaction_number.clear()
        self.amount.setValue(0.0)
        self.tax_amount.setValue(0.0)
        self.discount_amount.setValue(0.0)
        self.total_amount.setValue(0.0)
        self.paid_amount.setValue(0.0)
        self.remaining_amount.setValue(0.0)
        self.notes.clear()
    
    def close_screen(self):
        """إغلاق الشاشة"""
        if hasattr(self, 'main_window'):
            self.window().close()
        else:
            self.parent().close()
