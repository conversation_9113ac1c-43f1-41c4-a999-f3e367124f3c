# -*- coding: utf-8 -*-
"""
نماذج الأصناف
Items Models for ShipmentPro System
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Float, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from . import Base


class UnitOfMeasure(Base):
    """جدول وحدات القياس"""
    __tablename__ = 'units_of_measure'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(20), unique=True, nullable=False, comment="رمز الوحدة")
    name = Column(String(100), nullable=False, comment="اسم الوحدة")
    name_en = Column(String(100), comment="اسم الوحدة بالإنجليزية")
    description = Column(Text, comment="وصف الوحدة")
    is_active = Column(Boolean, default=True, comment="الوحدة نشطة")
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="تاريخ التحديث")
    
    # العلاقات
    items = relationship("Item", back_populates="unit_of_measure")
    
    def __repr__(self):
        return f"<UnitOfMeasure(code='{self.code}', name='{self.name}')>"


class ItemGroup(Base):
    """جدول مجموعات الأصناف"""
    __tablename__ = 'item_groups'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(20), unique=True, nullable=False, comment="رمز المجموعة")
    name = Column(String(200), nullable=False, comment="اسم المجموعة")
    name_en = Column(String(200), comment="اسم المجموعة بالإنجليزية")
    parent_id = Column(Integer, ForeignKey('item_groups.id'), comment="المجموعة الأب")
    description = Column(Text, comment="وصف المجموعة")
    is_active = Column(Boolean, default=True, comment="المجموعة نشطة")
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="تاريخ التحديث")
    
    # العلاقات
    parent = relationship("ItemGroup", remote_side=[id], backref="children")
    items = relationship("Item", back_populates="item_group")
    
    def __repr__(self):
        return f"<ItemGroup(code='{self.code}', name='{self.name}')>"


class Item(Base):
    """جدول الأصناف"""
    __tablename__ = 'items'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(50), unique=True, nullable=False, comment="رمز الصنف")
    name = Column(String(300), nullable=False, comment="اسم الصنف")
    name_en = Column(String(300), comment="اسم الصنف بالإنجليزية")
    barcode = Column(String(100), comment="الباركود")
    item_group_id = Column(Integer, ForeignKey('item_groups.id'), comment="مجموعة الصنف")
    unit_of_measure_id = Column(Integer, ForeignKey('units_of_measure.id'), comment="وحدة القياس")
    
    # البيانات الأساسية
    description = Column(Text, comment="وصف الصنف")
    specifications = Column(Text, comment="المواصفات")
    origin_country = Column(String(100), comment="بلد المنشأ")
    brand = Column(String(100), comment="العلامة التجارية")
    model = Column(String(100), comment="الموديل")
    
    # بيانات التكلفة والسعر
    purchase_price = Column(Float, default=0.0, comment="سعر الشراء")
    selling_price = Column(Float, default=0.0, comment="سعر البيع")
    min_selling_price = Column(Float, default=0.0, comment="أقل سعر بيع")
    wholesale_price = Column(Float, default=0.0, comment="سعر الجملة")
    
    # بيانات المخزون
    current_stock = Column(Float, default=0.0, comment="المخزون الحالي")
    min_stock_level = Column(Float, default=0.0, comment="الحد الأدنى للمخزون")
    max_stock_level = Column(Float, default=0.0, comment="الحد الأقصى للمخزون")
    reorder_point = Column(Float, default=0.0, comment="نقطة إعادة الطلب")
    
    # بيانات فيزيائية
    weight = Column(Float, comment="الوزن")
    length = Column(Float, comment="الطول")
    width = Column(Float, comment="العرض")
    height = Column(Float, comment="الارتفاع")
    volume = Column(Float, comment="الحجم")
    
    # خيارات متقدمة
    is_active = Column(Boolean, default=True, comment="الصنف نشط")
    is_sellable = Column(Boolean, default=True, comment="قابل للبيع")
    is_purchasable = Column(Boolean, default=True, comment="قابل للشراء")
    track_serial_numbers = Column(Boolean, default=False, comment="تتبع الأرقام التسلسلية")
    track_expiry_date = Column(Boolean, default=False, comment="تتبع تاريخ الانتهاء")
    
    # بيانات ضريبية
    tax_rate = Column(Float, default=0.0, comment="معدل الضريبة")
    tax_exempt = Column(Boolean, default=False, comment="معفى من الضريبة")
    
    # بيانات إضافية
    notes = Column(Text, comment="ملاحظات")
    image_path = Column(String(500), comment="مسار الصورة")
    
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="تاريخ التحديث")
    
    # العلاقات
    item_group = relationship("ItemGroup", back_populates="items")
    unit_of_measure = relationship("UnitOfMeasure", back_populates="items")
    costs = relationship("ItemCost", back_populates="item")
    
    def __repr__(self):
        return f"<Item(code='{self.code}', name='{self.name}')>"


class ItemCost(Base):
    """جدول تكاليف الأصناف"""
    __tablename__ = 'item_costs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    item_id = Column(Integer, ForeignKey('items.id'), nullable=False, comment="معرف الصنف")
    cost_type = Column(String(50), nullable=False, comment="نوع التكلفة")
    cost_name = Column(String(200), nullable=False, comment="اسم التكلفة")
    cost_value = Column(Float, default=0.0, comment="قيمة التكلفة")
    cost_percentage = Column(Float, default=0.0, comment="نسبة التكلفة")
    is_percentage = Column(Boolean, default=False, comment="التكلفة بالنسبة")
    is_active = Column(Boolean, default=True, comment="التكلفة نشطة")
    description = Column(Text, comment="وصف التكلفة")
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="تاريخ التحديث")
    
    # العلاقات
    item = relationship("Item", back_populates="costs")
    
    def __repr__(self):
        return f"<ItemCost(item_id={self.item_id}, cost_name='{self.cost_name}')>"


class ItemPriceHistory(Base):
    """جدول تاريخ أسعار الأصناف"""
    __tablename__ = 'item_price_history'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    item_id = Column(Integer, ForeignKey('items.id'), nullable=False, comment="معرف الصنف")
    price_type = Column(String(50), nullable=False, comment="نوع السعر")
    old_price = Column(Float, comment="السعر القديم")
    new_price = Column(Float, comment="السعر الجديد")
    change_reason = Column(String(255), comment="سبب التغيير")
    changed_by = Column(Integer, ForeignKey('users.id'), comment="من غير السعر")
    changed_at = Column(DateTime, default=func.now(), comment="تاريخ التغيير")
    
    def __repr__(self):
        return f"<ItemPriceHistory(item_id={self.item_id}, price_type='{self.price_type}')>"
