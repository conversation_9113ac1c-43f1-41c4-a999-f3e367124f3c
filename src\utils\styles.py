# -*- coding: utf-8 -*-
"""
أنماط واجهة المستخدم
UI Styles for ShipmentPro System
"""

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QAbstractItemView


class AppStyles:
    """فئة أنماط التطبيق"""
    
    # الألوان الأساسية
    PRIMARY_COLOR = "#2c3e50"
    SECONDARY_COLOR = "#3498db"
    SUCCESS_COLOR = "#27ae60"
    WARNING_COLOR = "#f39c12"
    DANGER_COLOR = "#e74c3c"
    INFO_COLOR = "#17a2b8"
    LIGHT_COLOR = "#ecf0f1"
    DARK_COLOR = "#34495e"
    
    @staticmethod
    def get_main_window_style():
        """نمط النافذة الرئيسية"""
        return f"""
        QMainWindow {{
            background-color: {AppStyles.LIGHT_COLOR};
            color: {AppStyles.DARK_COLOR};
            font-family: 'Tahoma', 'Arial Unicode MS', sans-serif;
        }}
        
        QMenuBar {{
            background-color: {AppStyles.PRIMARY_COLOR};
            color: white;
            border: none;
            padding: 4px;
        }}
        
        QMenuBar::item {{
            background-color: transparent;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
        }}
        
        QMenuBar::item:selected {{
            background-color: {AppStyles.SECONDARY_COLOR};
        }}
        
        QMenu {{
            background-color: white;
            border: 1px solid {AppStyles.PRIMARY_COLOR};
            padding: 4px;
        }}
        
        QMenu::item {{
            padding: 8px 20px;
            border-radius: 4px;
        }}
        
        QMenu::item:selected {{
            background-color: {AppStyles.SECONDARY_COLOR};
            color: white;
        }}
        
        QToolBar {{
            background-color: {AppStyles.LIGHT_COLOR};
            border: 1px solid #bdc3c7;
            padding: 4px;
            spacing: 4px;
        }}
        
        QStatusBar {{
            background-color: {AppStyles.PRIMARY_COLOR};
            color: white;
            border: none;
        }}
        """
    
    @staticmethod
    def get_sidebar_title_style():
        """نمط عنوان القائمة الجانبية"""
        return f"""
        QLabel {{
            background-color: {AppStyles.PRIMARY_COLOR};
            color: white;
            font-weight: bold;
            font-size: 14px;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 5px;
        }}
        """
    
    @staticmethod
    def get_content_title_style():
        """نمط عنوان المحتوى"""
        return f"""
        QLabel {{
            background-color: {AppStyles.SECONDARY_COLOR};
            color: white;
            font-weight: bold;
            font-size: 16px;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 10px;
        }}
        """
    
    @staticmethod
    def get_tree_style():
        """نمط شجرة القوائم"""
        return f"""
        QTreeWidget {{
            background-color: white;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            font-size: 12px;
            outline: none;
        }}
        
        QTreeWidget::item {{
            padding: 8px;
            border-bottom: 1px solid #ecf0f1;
        }}
        
        QTreeWidget::item:selected {{
            background-color: {AppStyles.SECONDARY_COLOR};
            color: white;
        }}
        
        QTreeWidget::item:hover {{
            background-color: #e8f4fd;
        }}
        
        QTreeWidget::branch:has-children:!has-siblings:closed,
        QTreeWidget::branch:closed:has-children:has-siblings {{
            border-image: none;
            image: url(assets/icons/branch-closed.png);
        }}
        
        QTreeWidget::branch:open:has-children:!has-siblings,
        QTreeWidget::branch:open:has-children:has-siblings {{
            border-image: none;
            image: url(assets/icons/branch-open.png);
        }}
        """
    
    @staticmethod
    def get_button_style():
        """نمط الأزرار"""
        return f"""
        QPushButton {{
            background-color: {AppStyles.SECONDARY_COLOR};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            min-width: 80px;
        }}
        
        QPushButton:hover {{
            background-color: #2980b9;
        }}
        
        QPushButton:pressed {{
            background-color: #21618c;
        }}
        
        QPushButton:disabled {{
            background-color: #bdc3c7;
            color: #7f8c8d;
        }}
        """

    @staticmethod
    def get_button_style(button_type="secondary"):
        """نمط الأزرار مع إمكانية تحديد النوع"""
        styles = {
            "primary": AppStyles.get_primary_button_style(),
            "secondary": AppStyles.get_button_style_base(),
            "success": AppStyles.get_success_button_style(),
            "danger": AppStyles.get_danger_button_style(),
            "warning": AppStyles.get_warning_button_style(),
            "info": AppStyles.get_info_button_style()
        }
        return styles.get(button_type, styles["secondary"])

    @staticmethod
    def get_button_style_base():
        """النمط الأساسي للأزرار"""
        return f"""
        QPushButton {{
            background-color: {AppStyles.SECONDARY_COLOR};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            min-width: 80px;
        }}

        QPushButton:hover {{
            background-color: #2980b9;
        }}

        QPushButton:pressed {{
            background-color: #21618c;
        }}

        QPushButton:disabled {{
            background-color: #bdc3c7;
            color: #7f8c8d;
        }}
        """

    @staticmethod
    def get_primary_button_style():
        """نمط الزر الأساسي"""
        return f"""
        QPushButton {{
            background-color: {AppStyles.PRIMARY_COLOR};
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 13px;
            min-width: 100px;
        }}
        
        QPushButton:hover {{
            background-color: #1a252f;
        }}
        
        QPushButton:pressed {{
            background-color: #0f1419;
        }}
        """
    
    @staticmethod
    def get_success_button_style():
        """نمط زر النجاح"""
        return f"""
        QPushButton {{
            background-color: {AppStyles.SUCCESS_COLOR};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            min-width: 80px;
        }}
        
        QPushButton:hover {{
            background-color: #229954;
        }}
        
        QPushButton:pressed {{
            background-color: #1e8449;
        }}
        """
    
    @staticmethod
    def get_danger_button_style():
        """نمط زر الخطر"""
        return f"""
        QPushButton {{
            background-color: {AppStyles.DANGER_COLOR};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            min-width: 80px;
        }}

        QPushButton:hover {{
            background-color: #c0392b;
        }}

        QPushButton:pressed {{
            background-color: #a93226;
        }}
        """

    @staticmethod
    def get_warning_button_style():
        """نمط زر التحذير"""
        return f"""
        QPushButton {{
            background-color: {AppStyles.WARNING_COLOR};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            min-width: 80px;
        }}

        QPushButton:hover {{
            background-color: #e67e22;
        }}

        QPushButton:pressed {{
            background-color: #d35400;
        }}
        """

    @staticmethod
    def get_info_button_style():
        """نمط زر المعلومات"""
        return f"""
        QPushButton {{
            background-color: {AppStyles.INFO_COLOR};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            min-width: 80px;
        }}

        QPushButton:hover {{
            background-color: #2980b9;
        }}

        QPushButton:pressed {{
            background-color: #21618c;
        }}
        """

    @staticmethod
    def get_color(color_name):
        """الحصول على لون بناءً على الاسم"""
        from PySide6.QtGui import QColor

        colors = {
            "primary": QColor(AppStyles.PRIMARY_COLOR),
            "primary_light": QColor("#34495e"),
            "secondary": QColor(AppStyles.SECONDARY_COLOR),
            "secondary_light": QColor("#5dade2"),
            "success": QColor(AppStyles.SUCCESS_COLOR),
            "success_light": QColor("#58d68d"),
            "danger": QColor(AppStyles.DANGER_COLOR),
            "danger_light": QColor("#ec7063"),
            "warning": QColor(AppStyles.WARNING_COLOR),
            "warning_light": QColor("#f7dc6f"),
            "info": QColor(AppStyles.INFO_COLOR),
            "info_light": QColor("#85c1e9"),
            "light": QColor("#f8f9fa"),
            "dark": QColor("#343a40")
        }
        return colors.get(color_name, QColor("#ffffff"))

    @staticmethod
    def get_input_style():
        """نمط حقول الإدخال"""
        return f"""
        QLineEdit, QTextEdit, QPlainTextEdit {{
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            padding: 8px;
            font-size: 12px;
            background-color: white;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {AppStyles.SECONDARY_COLOR};
        }}
        
        QComboBox {{
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            padding: 6px;
            font-size: 12px;
            background-color: white;
            min-width: 120px;
        }}
        
        QComboBox:focus {{
            border-color: {AppStyles.SECONDARY_COLOR};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        
        QComboBox::down-arrow {{
            image: url(assets/icons/down-arrow.png);
            width: 12px;
            height: 12px;
        }}
        """
    
    @staticmethod
    def get_table_style():
        """نمط الجداول"""
        return f"""
        QTableWidget {{
            gridline-color: #bdc3c7;
            background-color: white;
            alternate-background-color: #f8f9fa;
            selection-background-color: {AppStyles.SECONDARY_COLOR};
            border: 1px solid #bdc3c7;
            border-radius: 4px;
        }}
        
        QTableWidget::item {{
            padding: 8px;
            border: none;
        }}
        
        QTableWidget::item:selected {{
            background-color: {AppStyles.SECONDARY_COLOR};
            color: white;
        }}
        
        QHeaderView::section {{
            background-color: {AppStyles.PRIMARY_COLOR};
            color: white;
            padding: 12px 8px;
            border: none;
            font-weight: bold;
            font-size: 12px;
            min-height: 35px;
            text-align: center;
        }}

        QHeaderView::section:hover {{
            background-color: {AppStyles.DARK_COLOR};
        }}

        QTableWidget::item {{
            padding: 10px 8px;
            border: none;
            min-height: 25px;
        }}
        """

    @staticmethod
    def setup_table_widget(table_widget):
        """إعداد جدول بالأنماط والإعدادات الصحيحة"""
        # تطبيق النمط
        table_widget.setStyleSheet(AppStyles.get_table_style())

        # إعدادات العناوين
        header = table_widget.horizontalHeader()
        header.setDefaultSectionSize(120)
        header.setMinimumSectionSize(80)
        header.setStretchLastSection(True)
        header.setDefaultAlignment(Qt.AlignCenter)

        # إعدادات الصفوف
        table_widget.verticalHeader().setDefaultSectionSize(35)
        table_widget.verticalHeader().setMinimumSectionSize(30)

        # إعدادات عامة
        table_widget.setAlternatingRowColors(True)
        table_widget.setSelectionBehavior(QAbstractItemView.SelectRows)
        table_widget.setGridStyle(Qt.SolidLine)

        return table_widget

    @staticmethod
    def get_tab_style():
        """نمط التبويبات"""
        return f"""
        QTabWidget::pane {{
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            background-color: white;
        }}
        
        QTabBar::tab {{
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {AppStyles.SECONDARY_COLOR};
            color: white;
            border-bottom: none;
        }}
        
        QTabBar::tab:hover:!selected {{
            background-color: #d5dbdb;
        }}
        """
    
    @staticmethod
    def get_group_box_style():
        """نمط صناديق المجموعات"""
        return f"""
        QGroupBox {{
            font-weight: bold;
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            margin-top: 10px;
            padding-top: 10px;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            background-color: white;
            color: {AppStyles.PRIMARY_COLOR};
        }}
        """
    
    @staticmethod
    def get_frame_style():
        """نمط الإطارات"""
        return f"""
        QFrame {{
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            background-color: white;
            padding: 5px;
        }}
        """
