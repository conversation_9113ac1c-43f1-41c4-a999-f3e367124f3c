# -*- coding: utf-8 -*-
"""
نماذج الموردين
Suppliers Models for ShipmentPro System
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Float, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from . import Base


class Supplier(Base):
    """جدول الموردين"""
    __tablename__ = 'suppliers'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(50), unique=True, nullable=False, comment="رمز المورد")
    name = Column(String(300), nullable=False, comment="اسم المورد")
    name_en = Column(String(300), comment="اسم المورد بالإنجليزية")
    
    # بيانات الاتصال
    contact_person = Column(String(200), comment="الشخص المسؤول")
    phone = Column(String(50), comment="الهاتف")
    mobile = Column(String(50), comment="الجوال")
    fax = Column(String(50), comment="الفاكس")
    email = Column(String(100), comment="البريد الإلكتروني")
    website = Column(String(200), comment="الموقع الإلكتروني")
    
    # بيانات العنوان
    address = Column(Text, comment="العنوان")
    city = Column(String(100), comment="المدينة")
    state = Column(String(100), comment="المنطقة/الولاية")
    country = Column(String(100), comment="الدولة")
    postal_code = Column(String(20), comment="الرمز البريدي")
    
    # بيانات تجارية
    commercial_register = Column(String(50), comment="السجل التجاري")
    tax_number = Column(String(50), comment="الرقم الضريبي")
    license_number = Column(String(50), comment="رقم الترخيص")
    
    # بيانات مالية
    credit_limit = Column(Float, default=0.0, comment="حد الائتمان")
    payment_terms = Column(String(100), comment="شروط الدفع")
    currency_id = Column(Integer, ForeignKey('currencies.id'), comment="العملة")
    
    # تصنيف المورد
    supplier_type = Column(String(50), comment="نوع المورد")
    supplier_category = Column(String(100), comment="فئة المورد")
    rating = Column(Integer, comment="تقييم المورد (1-5)")
    
    # حالة المورد
    is_active = Column(Boolean, default=True, comment="المورد نشط")
    is_approved = Column(Boolean, default=False, comment="المورد معتمد")
    approval_date = Column(DateTime, comment="تاريخ الاعتماد")
    
    # بيانات إضافية
    notes = Column(Text, comment="ملاحظات")
    internal_notes = Column(Text, comment="ملاحظات داخلية")
    
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="تاريخ التحديث")
    
    # العلاقات
    currency = relationship("Currency")
    transactions = relationship("SupplierTransaction", back_populates="supplier")
    
    def __repr__(self):
        return f"<Supplier(code='{self.code}', name='{self.name}')>"


class SupplierTransaction(Base):
    """جدول عمليات الموردين"""
    __tablename__ = 'supplier_transactions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False, comment="معرف المورد")
    transaction_number = Column(String(50), unique=True, nullable=False, comment="رقم العملية")
    transaction_type = Column(String(50), nullable=False, comment="نوع العملية")
    
    # بيانات العملية
    transaction_date = Column(DateTime, nullable=False, comment="تاريخ العملية")
    due_date = Column(DateTime, comment="تاريخ الاستحقاق")
    reference_number = Column(String(100), comment="الرقم المرجعي")
    
    # المبالغ
    amount = Column(Float, default=0.0, comment="المبلغ")
    paid_amount = Column(Float, default=0.0, comment="المبلغ المدفوع")
    remaining_amount = Column(Float, default=0.0, comment="المبلغ المتبقي")
    currency_id = Column(Integer, ForeignKey('currencies.id'), comment="العملة")
    exchange_rate = Column(Float, default=1.0, comment="سعر الصرف")
    
    # حالة العملية
    status = Column(String(50), default='pending', comment="حالة العملية")
    is_paid = Column(Boolean, default=False, comment="مدفوعة")
    payment_date = Column(DateTime, comment="تاريخ الدفع")
    
    # بيانات إضافية
    description = Column(Text, comment="وصف العملية")
    notes = Column(Text, comment="ملاحظات")
    
    # بيانات المستخدم
    created_by = Column(Integer, ForeignKey('users.id'), comment="أنشأ بواسطة")
    approved_by = Column(Integer, ForeignKey('users.id'), comment="اعتمد بواسطة")
    
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="تاريخ التحديث")
    
    # العلاقات
    supplier = relationship("Supplier", back_populates="transactions")
    currency = relationship("Currency")
    creator = relationship("User", foreign_keys=[created_by])
    approver = relationship("User", foreign_keys=[approved_by])
    
    def __repr__(self):
        return f"<SupplierTransaction(number='{self.transaction_number}', type='{self.transaction_type}')>"


class SupplierContact(Base):
    """جدول جهات اتصال الموردين"""
    __tablename__ = 'supplier_contacts'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False, comment="معرف المورد")
    name = Column(String(200), nullable=False, comment="اسم جهة الاتصال")
    position = Column(String(100), comment="المنصب")
    department = Column(String(100), comment="القسم")
    phone = Column(String(50), comment="الهاتف")
    mobile = Column(String(50), comment="الجوال")
    email = Column(String(100), comment="البريد الإلكتروني")
    is_primary = Column(Boolean, default=False, comment="جهة الاتصال الرئيسية")
    is_active = Column(Boolean, default=True, comment="نشط")
    notes = Column(Text, comment="ملاحظات")
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="تاريخ التحديث")
    
    # العلاقات
    supplier = relationship("Supplier")
    
    def __repr__(self):
        return f"<SupplierContact(name='{self.name}', supplier_id={self.supplier_id})>"


class SupplierDocument(Base):
    """جدول مستندات الموردين"""
    __tablename__ = 'supplier_documents'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False, comment="معرف المورد")
    document_type = Column(String(100), nullable=False, comment="نوع المستند")
    document_name = Column(String(300), nullable=False, comment="اسم المستند")
    file_path = Column(String(500), comment="مسار الملف")
    file_size = Column(Integer, comment="حجم الملف")
    file_type = Column(String(50), comment="نوع الملف")
    expiry_date = Column(DateTime, comment="تاريخ الانتهاء")
    is_required = Column(Boolean, default=False, comment="مستند مطلوب")
    is_verified = Column(Boolean, default=False, comment="مستند محقق")
    verified_by = Column(Integer, ForeignKey('users.id'), comment="تم التحقق بواسطة")
    verified_at = Column(DateTime, comment="تاريخ التحقق")
    notes = Column(Text, comment="ملاحظات")
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="تاريخ التحديث")
    
    # العلاقات
    supplier = relationship("Supplier")
    verifier = relationship("User")
    
    def __repr__(self):
        return f"<SupplierDocument(name='{self.document_name}', type='{self.document_type}')>"
