# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية لنظام إدارة التكاليف
Main Costs Management Interface
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                               QPushButton, QLabel, QFrame, QMessageBox, QGroupBox,
                               QTableWidget, QTableWidgetItem, QLineEdit, QComboBox,
                               QDateEdit, QTextEdit, QFormLayout, QGridLayout, QSpinBox,
                               QDoubleSpinBox, QCheckBox, QTreeWidget, QTreeWidgetItem,
                               QInputDialog)
from PySide6.QtCore import Qt, QDate

from ...utils.arabic_support import ArabicSupport
from ...utils.styles import AppStyles


class CostsMainWidget(QWidget):
    """الواجهة الرئيسية لنظام إدارة التكاليف"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النظام
        title = QLabel("نظام إدارة التكاليف")
        title.setStyleSheet("font-weight: bold; font-size: 18px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)

        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setLayoutDirection(Qt.RightToLeft)
        self.tab_widget.setStyleSheet(AppStyles.get_tab_style())

        # تبويب تكاليف الشحنات
        shipment_costs_tab = self.create_shipment_costs_tab()
        self.tab_widget.addTab(shipment_costs_tab, "تكاليف الشحنات")

        # تبويب المصروفات الإضافية
        additional_costs_tab = self.create_additional_costs_tab()
        self.tab_widget.addTab(additional_costs_tab, "المصروفات الإضافية")

        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        self.tab_widget.addTab(reports_tab, "التقارير")

        main_layout.addWidget(self.tab_widget)
        
        # إضافة أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.StyledPanel)
        buttons_frame.setStyleSheet(AppStyles.get_frame_style())
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        
        # الأزرار الأساسية
        self.add_btn = QPushButton("إضافة")
        self.add_btn.setStyleSheet(AppStyles.get_success_button_style())
        self.add_btn.clicked.connect(self.add_record)

        self.edit_btn = QPushButton("تعديل")
        self.edit_btn.setStyleSheet(AppStyles.get_button_style())
        self.edit_btn.clicked.connect(self.edit_record)

        self.save_btn = QPushButton("حفظ")
        self.save_btn.setStyleSheet(AppStyles.get_success_button_style())
        self.save_btn.clicked.connect(self.save_record)

        self.search_btn = QPushButton("بحث")
        self.search_btn.setStyleSheet(AppStyles.get_button_style())
        self.search_btn.clicked.connect(self.search_records)

        self.delete_btn = QPushButton("حذف")
        self.delete_btn.setStyleSheet(AppStyles.get_danger_button_style())
        self.delete_btn.clicked.connect(self.delete_record)

        self.exit_btn = QPushButton("خروج")
        self.exit_btn.setStyleSheet(AppStyles.get_primary_button_style())
        self.exit_btn.clicked.connect(self.close_costs)

        # إضافة الأزرار للتخطيط
        buttons_layout.addWidget(self.add_btn)
        buttons_layout.addWidget(self.edit_btn)
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.search_btn)
        buttons_layout.addWidget(self.delete_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.exit_btn)
        
        layout.addWidget(buttons_frame)

    def create_shipment_costs_tab(self):
        """إنشاء تبويب تكاليف الشحنات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة بيانات الشحنة
        shipment_info_group = QGroupBox("بيانات الشحنة")
        shipment_info_group.setStyleSheet(AppStyles.get_frame_style())
        shipment_info_layout = QFormLayout(shipment_info_group)

        self.shipment_number = QLineEdit()
        self.shipment_number.setPlaceholderText("رقم الشحنة...")
        shipment_info_layout.addRow("رقم الشحنة:", self.shipment_number)

        self.shipment_date = QDateEdit()
        self.shipment_date.setDate(QDate.currentDate())
        self.shipment_date.setCalendarPopup(True)
        shipment_info_layout.addRow("تاريخ الشحنة:", self.shipment_date)

        self.shipment_weight = QDoubleSpinBox()
        self.shipment_weight.setMaximum(999999.99)
        self.shipment_weight.setSuffix(" كيلو")
        shipment_info_layout.addRow("الوزن:", self.shipment_weight)

        self.shipment_volume = QDoubleSpinBox()
        self.shipment_volume.setMaximum(999999.99)
        self.shipment_volume.setSuffix(" متر مكعب")
        shipment_info_layout.addRow("الحجم:", self.shipment_volume)

        layout.addWidget(shipment_info_group)

        # مجموعة التكاليف الأساسية
        basic_costs_group = QGroupBox("التكاليف الأساسية")
        basic_costs_group.setStyleSheet(AppStyles.get_frame_style())
        basic_costs_layout = QFormLayout(basic_costs_group)

        self.shipping_cost = QDoubleSpinBox()
        self.shipping_cost.setMaximum(999999999.99)
        self.shipping_cost.setSuffix(" ريال")
        basic_costs_layout.addRow("تكلفة الشحن:", self.shipping_cost)

        self.insurance_cost = QDoubleSpinBox()
        self.insurance_cost.setMaximum(999999999.99)
        self.insurance_cost.setSuffix(" ريال")
        basic_costs_layout.addRow("تكلفة التأمين:", self.insurance_cost)

        self.handling_cost = QDoubleSpinBox()
        self.handling_cost.setMaximum(999999999.99)
        self.handling_cost.setSuffix(" ريال")
        basic_costs_layout.addRow("تكلفة المناولة:", self.handling_cost)

        self.storage_cost = QDoubleSpinBox()
        self.storage_cost.setMaximum(999999999.99)
        self.storage_cost.setSuffix(" ريال")
        basic_costs_layout.addRow("تكلفة التخزين:", self.storage_cost)

        layout.addWidget(basic_costs_group)

        # مجموعة التكاليف الإضافية
        additional_costs_group = QGroupBox("التكاليف الإضافية")
        additional_costs_group.setStyleSheet(AppStyles.get_frame_style())
        additional_costs_layout = QVBoxLayout(additional_costs_group)

        self.additional_costs_table = QTableWidget()
        self.additional_costs_table.setColumnCount(4)
        self.additional_costs_table.setHorizontalHeaderLabels([
            "نوع التكلفة", "الوصف", "المبلغ", "ملاحظات"
        ])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.additional_costs_table)

        # إضافة بيانات تجريبية
        self.additional_costs_table.setRowCount(3)
        additional_costs_data = [
            ["رسوم جمركية", "رسوم التخليص الجمركي", "1,500.00", "5% من قيمة البضاعة"],
            ["رسوم ميناء", "رسوم استخدام المرافق", "800.00", "رسوم ثابتة"],
            ["رسوم فحص", "فحص البضاعة", "300.00", "فحص أمني"]
        ]

        for row, data in enumerate(additional_costs_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 2:  # عمود المبلغ
                    item.setForeground(Qt.darkGreen)
                self.additional_costs_table.setItem(row, col, item)

        additional_costs_layout.addWidget(self.additional_costs_table)
        layout.addWidget(additional_costs_group)

        # مجموعة إجمالي التكاليف
        total_costs_group = QGroupBox("إجمالي التكاليف")
        total_costs_group.setStyleSheet(AppStyles.get_frame_style())
        total_costs_layout = QFormLayout(total_costs_group)

        self.subtotal = QLineEdit()
        self.subtotal.setReadOnly(True)
        self.subtotal.setText("0.00 ريال")
        total_costs_layout.addRow("المجموع الفرعي:", self.subtotal)

        self.tax_amount = QLineEdit()
        self.tax_amount.setReadOnly(True)
        self.tax_amount.setText("0.00 ريال")
        total_costs_layout.addRow("الضريبة (15%):", self.tax_amount)

        self.total_amount = QLineEdit()
        self.total_amount.setReadOnly(True)
        self.total_amount.setText("0.00 ريال")
        self.total_amount.setStyleSheet("font-weight: bold; background-color: #ecf0f1; font-size: 14px;")
        total_costs_layout.addRow("الإجمالي النهائي:", self.total_amount)

        calculate_total_btn = QPushButton("حساب الإجمالي")
        calculate_total_btn.setStyleSheet(AppStyles.get_success_button_style())
        total_costs_layout.addRow("", calculate_total_btn)

        layout.addWidget(total_costs_group)

        return widget

    def create_additional_costs_tab(self):
        """إنشاء تبويب المصروفات الإضافية"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة إضافة مصروف جديد
        new_expense_group = QGroupBox("إضافة مصروف جديد")
        new_expense_group.setStyleSheet(AppStyles.get_frame_style())
        new_expense_layout = QFormLayout(new_expense_group)

        self.expense_date = QDateEdit()
        self.expense_date.setDate(QDate.currentDate())
        self.expense_date.setCalendarPopup(True)
        new_expense_layout.addRow("التاريخ:", self.expense_date)

        self.expense_category = QComboBox()
        self.expense_category.addItems([
            "اختر الفئة", "مصروفات إدارية", "مصروفات تشغيلية", "رسوم حكومية",
            "مصروفات نقل", "مصروفات تخزين", "مصروفات أخرى"
        ])
        new_expense_layout.addRow("فئة المصروف:", self.expense_category)

        self.expense_description = QLineEdit()
        self.expense_description.setPlaceholderText("وصف المصروف...")
        new_expense_layout.addRow("الوصف:", self.expense_description)

        self.expense_amount = QDoubleSpinBox()
        self.expense_amount.setMaximum(999999999.99)
        self.expense_amount.setSuffix(" ريال")
        new_expense_layout.addRow("المبلغ:", self.expense_amount)

        self.expense_reference = QLineEdit()
        self.expense_reference.setPlaceholderText("رقم الشحنة أو المرجع...")
        new_expense_layout.addRow("المرجع:", self.expense_reference)

        add_expense_btn = QPushButton("إضافة المصروف")
        add_expense_btn.setStyleSheet(AppStyles.get_success_button_style())
        new_expense_layout.addRow("", add_expense_btn)

        layout.addWidget(new_expense_group)

        # مجموعة قائمة المصروفات
        expenses_list_group = QGroupBox("قائمة المصروفات")
        expenses_list_group.setStyleSheet(AppStyles.get_frame_style())
        expenses_list_layout = QVBoxLayout(expenses_list_group)

        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(6)
        self.expenses_table.setHorizontalHeaderLabels([
            "التاريخ", "الفئة", "الوصف", "المبلغ", "المرجع", "الحالة"
        ])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.expenses_table)

        # إضافة بيانات تجريبية
        self.expenses_table.setRowCount(5)
        expenses_data = [
            ["2024-01-15", "رسوم حكومية", "رسوم تخليص جمركي", "1,500.00", "SH-001", "مدفوع"],
            ["2024-01-16", "مصروفات نقل", "نقل من المطار", "800.00", "SH-001", "مدفوع"],
            ["2024-01-18", "مصروفات تخزين", "تخزين لمدة 3 أيام", "450.00", "SH-002", "معلق"],
            ["2024-01-20", "مصروفات إدارية", "رسوم إدارية", "200.00", "SH-002", "مدفوع"],
            ["2024-01-22", "مصروفات أخرى", "رسوم فحص إضافية", "300.00", "SH-003", "معلق"]
        ]

        for row, data in enumerate(expenses_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 3:  # عمود المبلغ
                    item.setForeground(Qt.darkGreen)
                elif col == 5:  # عمود الحالة
                    if value == "مدفوع":
                        item.setBackground(Qt.green)
                    elif value == "معلق":
                        item.setBackground(Qt.yellow)
                self.expenses_table.setItem(row, col, item)

        expenses_list_layout.addWidget(self.expenses_table)
        layout.addWidget(expenses_list_group)

        return widget

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة أنواع التقارير
        reports_types_group = QGroupBox("أنواع تقارير التكاليف")
        reports_types_group.setStyleSheet(AppStyles.get_frame_style())
        reports_types_layout = QGridLayout(reports_types_group)

        # تقرير التكاليف الشهرية
        monthly_costs_btn = QPushButton("تقرير التكاليف الشهرية")
        monthly_costs_btn.setStyleSheet(AppStyles.get_button_style())
        reports_types_layout.addWidget(monthly_costs_btn, 0, 0)

        # تقرير التكاليف حسب الفئة
        category_costs_btn = QPushButton("تقرير التكاليف حسب الفئة")
        category_costs_btn.setStyleSheet(AppStyles.get_success_button_style())
        reports_types_layout.addWidget(category_costs_btn, 0, 1)

        # تقرير المصروفات المعلقة
        pending_expenses_btn = QPushButton("تقرير المصروفات المعلقة")
        pending_expenses_btn.setStyleSheet(AppStyles.get_danger_button_style())
        reports_types_layout.addWidget(pending_expenses_btn, 1, 0)

        # تقرير تحليل التكاليف
        cost_analysis_btn = QPushButton("تقرير تحليل التكاليف")
        cost_analysis_btn.setStyleSheet(AppStyles.get_primary_button_style())
        reports_types_layout.addWidget(cost_analysis_btn, 1, 1)

        layout.addWidget(reports_types_group)

        # مجموعة إعدادات التقرير
        report_settings_group = QGroupBox("إعدادات التقرير")
        report_settings_group.setStyleSheet(AppStyles.get_frame_style())
        report_settings_layout = QFormLayout(report_settings_group)

        self.report_from_date = QDateEdit()
        self.report_from_date.setDate(QDate.currentDate().addDays(-30))
        self.report_from_date.setCalendarPopup(True)
        report_settings_layout.addRow("من تاريخ:", self.report_from_date)

        self.report_to_date = QDateEdit()
        self.report_to_date.setDate(QDate.currentDate())
        self.report_to_date.setCalendarPopup(True)
        report_settings_layout.addRow("إلى تاريخ:", self.report_to_date)

        self.report_category = QComboBox()
        self.report_category.addItems([
            "جميع الفئات", "مصروفات إدارية", "مصروفات تشغيلية", "رسوم حكومية",
            "مصروفات نقل", "مصروفات تخزين", "مصروفات أخرى"
        ])
        report_settings_layout.addRow("فئة المصروف:", self.report_category)

        layout.addWidget(report_settings_group)

        # مجموعة ملخص التكاليف
        costs_summary_group = QGroupBox("ملخص التكاليف")
        costs_summary_group.setStyleSheet(AppStyles.get_frame_style())
        costs_summary_layout = QVBoxLayout(costs_summary_group)

        # شجرة ملخص التكاليف
        self.costs_summary_tree = QTreeWidget()
        self.costs_summary_tree.setHeaderLabels(["الفئة", "عدد المصروفات", "إجمالي المبلغ", "النسبة"])

        # إضافة بيانات تجريبية
        categories_data = [
            ("رسوم حكومية", "8", "12,000.00", "35%"),
            ("مصروفات نقل", "15", "9,500.00", "28%"),
            ("مصروفات تخزين", "12", "6,800.00", "20%"),
            ("مصروفات إدارية", "10", "3,200.00", "9%"),
            ("مصروفات أخرى", "5", "2,700.00", "8%")
        ]

        for category, count, amount, percentage in categories_data:
            item = QTreeWidgetItem([category, count, amount, percentage])
            if category == "رسوم حكومية":
                item.setBackground(0, Qt.lightGray)
            self.costs_summary_tree.addTopLevelItem(item)

        # إضافة صف الإجمالي
        total_item = QTreeWidgetItem(["الإجمالي", "50", "34,200.00", "100%"])
        total_item.setBackground(0, Qt.darkGray)
        total_item.setBackground(1, Qt.darkGray)
        total_item.setBackground(2, Qt.darkGray)
        total_item.setBackground(3, Qt.darkGray)
        self.costs_summary_tree.addTopLevelItem(total_item)

        costs_summary_layout.addWidget(self.costs_summary_tree)
        layout.addWidget(costs_summary_group)

        # مجموعة الرسم البياني
        chart_group = QGroupBox("الرسم البياني")
        chart_group.setStyleSheet(AppStyles.get_frame_style())
        chart_layout = QVBoxLayout(chart_group)

        chart_placeholder = QLabel("""
        📊 الرسم البياني للتكاليف - قيد التطوير

        سيتم إضافة الرسوم البيانية التالية:
        • رسم دائري لتوزيع التكاليف حسب الفئة
        • رسم بياني خطي لتطور التكاليف عبر الزمن
        • رسم بياني عمودي لمقارنة التكاليف الشهرية
        • مؤشرات الأداء الرئيسية (KPIs)
        """)
        chart_placeholder.setAlignment(Qt.AlignCenter)
        chart_placeholder.setStyleSheet("color: #7f8c8d; font-size: 14px; padding: 30px;")
        chart_placeholder.setWordWrap(True)
        chart_layout.addWidget(chart_placeholder)

        layout.addWidget(chart_group)

        # مجموعة خيارات التصدير
        export_group = QGroupBox("خيارات التصدير")
        export_group.setStyleSheet(AppStyles.get_frame_style())
        export_layout = QHBoxLayout(export_group)

        export_excel_btn = QPushButton("تصدير إلى Excel")
        export_excel_btn.setStyleSheet(AppStyles.get_success_button_style())
        export_layout.addWidget(export_excel_btn)

        export_pdf_btn = QPushButton("تصدير إلى PDF")
        export_pdf_btn.setStyleSheet(AppStyles.get_button_style())
        export_layout.addWidget(export_pdf_btn)

        print_report_btn = QPushButton("طباعة التقرير")
        print_report_btn.setStyleSheet(AppStyles.get_primary_button_style())
        export_layout.addWidget(print_report_btn)

        export_layout.addStretch()
        layout.addWidget(export_group)

        return widget

    def add_record(self):
        """إضافة سجل جديد"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 0:  # تبويب التكاليف الإضافية
            self.add_additional_cost()
        elif current_tab == 1:  # تبويب المصروفات
            self.add_expense()
        else:
            tab_name = self.tab_widget.tabText(current_tab)
            QMessageBox.information(self, "إضافة", f"إضافة سجل جديد في {tab_name}")

    def edit_record(self):
        """تعديل سجل"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 0:  # تبويب التكاليف الإضافية
            self.edit_additional_cost()
        elif current_tab == 1:  # تبويب المصروفات
            self.edit_expense()
        else:
            tab_name = self.tab_widget.tabText(current_tab)
            QMessageBox.information(self, "تعديل", f"تعديل سجل في {tab_name}")

    def save_record(self):
        """حفظ السجل"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 0:  # تبويب التكاليف الإضافية
            self.save_additional_cost()
        elif current_tab == 1:  # تبويب المصروفات
            self.save_expense()
        else:
            tab_name = self.tab_widget.tabText(current_tab)
            QMessageBox.information(self, "حفظ", f"تم حفظ البيانات في {tab_name} بنجاح")

    def search_records(self):
        """البحث في السجلات"""
        current_tab = self.tab_widget.currentIndex()
        tab_name = self.tab_widget.tabText(current_tab)

        # يمكن إضافة نافذة بحث متقدمة هنا
        search_text, ok = QInputDialog.getText(self, "البحث", f"البحث في {tab_name}:")
        if ok and search_text:
            QMessageBox.information(self, "البحث", f"البحث عن: {search_text}")

    def delete_record(self):
        """حذف سجل"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 0:  # تبويب التكاليف الإضافية
            self.delete_additional_cost()
        elif current_tab == 1:  # تبويب المصروفات
            self.delete_expense()
        else:
            tab_name = self.tab_widget.tabText(current_tab)
            QMessageBox.information(self, "حذف", f"حذف سجل من {tab_name}")

    def add_additional_cost(self):
        """إضافة تكلفة إضافية جديدة"""
        # إضافة صف جديد للجدول
        row_count = self.additional_costs_table.rowCount()
        self.additional_costs_table.insertRow(row_count)

        # إضافة بيانات افتراضية
        self.additional_costs_table.setItem(row_count, 0, QTableWidgetItem("تكلفة جديدة"))
        self.additional_costs_table.setItem(row_count, 1, QTableWidgetItem("وصف التكلفة"))
        self.additional_costs_table.setItem(row_count, 2, QTableWidgetItem("0.00"))
        self.additional_costs_table.setItem(row_count, 3, QTableWidgetItem("تكلفة ثابتة"))

        # تحديد الصف الجديد
        self.additional_costs_table.selectRow(row_count)

        QMessageBox.information(self, "نجح", "تم إضافة تكلفة إضافية جديدة")

    def edit_additional_cost(self):
        """تعديل تكلفة إضافية"""
        current_row = self.additional_costs_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد تكلفة للتعديل")
            return

        cost_name = self.additional_costs_table.item(current_row, 0).text()
        QMessageBox.information(self, "تعديل", f"يمكنك الآن تعديل التكلفة: {cost_name}\nانقر على الخلايا لتعديلها مباشرة")

    def save_additional_cost(self):
        """حفظ التكلفة الإضافية"""
        current_row = self.additional_costs_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد تكلفة للحفظ")
            return

        cost_name = self.additional_costs_table.item(current_row, 0).text()
        QMessageBox.information(self, "نجح", f"تم حفظ التكلفة: {cost_name}")

    def delete_additional_cost(self):
        """حذف تكلفة إضافية"""
        current_row = self.additional_costs_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد تكلفة للحذف")
            return

        cost_name = self.additional_costs_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل تريد حذف التكلفة '{cost_name}'؟\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.additional_costs_table.removeRow(current_row)
            QMessageBox.information(self, "نجح", f"تم حذف التكلفة: {cost_name}")

    def add_expense(self):
        """إضافة مصروف جديد"""
        # إضافة صف جديد للجدول
        row_count = self.expenses_table.rowCount()
        self.expenses_table.insertRow(row_count)

        # إضافة بيانات افتراضية
        current_date = QDate.currentDate().toString("yyyy-MM-dd")
        new_shipment_id = f"SH-{row_count + 4:03d}"

        self.expenses_table.setItem(row_count, 0, QTableWidgetItem(current_date))
        self.expenses_table.setItem(row_count, 1, QTableWidgetItem("مصروفات أخرى"))
        self.expenses_table.setItem(row_count, 2, QTableWidgetItem("مصروف جديد"))
        self.expenses_table.setItem(row_count, 3, QTableWidgetItem("0.00"))
        self.expenses_table.setItem(row_count, 4, QTableWidgetItem(new_shipment_id))
        self.expenses_table.setItem(row_count, 5, QTableWidgetItem("معلق"))

        # تحديد الصف الجديد
        self.expenses_table.selectRow(row_count)

        QMessageBox.information(self, "نجح", "تم إضافة مصروف جديد")

    def edit_expense(self):
        """تعديل مصروف"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مصروف للتعديل")
            return

        expense_desc = self.expenses_table.item(current_row, 2).text()
        QMessageBox.information(self, "تعديل", f"يمكنك الآن تعديل المصروف: {expense_desc}\nانقر على الخلايا لتعديلها مباشرة")

    def save_expense(self):
        """حفظ المصروف"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مصروف للحفظ")
            return

        expense_desc = self.expenses_table.item(current_row, 2).text()
        QMessageBox.information(self, "نجح", f"تم حفظ المصروف: {expense_desc}")

    def delete_expense(self):
        """حذف مصروف"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مصروف للحذف")
            return

        expense_desc = self.expenses_table.item(current_row, 2).text()

        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل تريد حذف المصروف '{expense_desc}'؟\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.expenses_table.removeRow(current_row)
            QMessageBox.information(self, "نجح", f"تم حذف المصروف: {expense_desc}")

    def close_costs(self):
        """إغلاق نافذة التكاليف"""
        if hasattr(self, 'main_window'):
            # إذا كانت النافذة مفتوحة في وضع ملء الشاشة
            self.window().close()
        else:
            # إذا كانت النافذة مدمجة في النافذة الرئيسية
            self.parent().show_module("welcome")
