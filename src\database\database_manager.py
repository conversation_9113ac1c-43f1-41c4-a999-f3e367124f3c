# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات الرئيسي
Database Manager for ShipmentPro System
"""

import os
import sqlite3
from pathlib import Path
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.exc import SQLAlchemyError

from .models import Base
from .models.settings import *
from .models.items import *
from .models.suppliers import *


class DatabaseManager:
    """مدير قاعدة البيانات الرئيسي"""
    
    def __init__(self, db_path=None):
        """
        تهيئة مدير قاعدة البيانات
        
        Args:
            db_path (str): مسار قاعدة البيانات (اختياري)
        """
        if db_path is None:
            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            data_dir = Path("data")
            data_dir.mkdir(exist_ok=True)
            db_path = data_dir / "shipmentpro.db"
        
        self.db_path = str(db_path)
        self.engine = None
        self.Session = None
        self._setup_database()
    
    def _setup_database(self):
        """إعداد اتصال قاعدة البيانات"""
        try:
            # إنشاء محرك قاعدة البيانات
            self.engine = create_engine(
                f"sqlite:///{self.db_path}",
                echo=False,  # تعيين True لرؤية استعلامات SQL
                pool_pre_ping=True,
                connect_args={"check_same_thread": False}
            )
            
            # إنشاء جلسة العمل
            self.Session = sessionmaker(bind=self.engine)
            
        except SQLAlchemyError as e:
            raise Exception(f"خطأ في إعداد قاعدة البيانات: {str(e)}")
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        try:
            # إنشاء جميع الجداول
            Base.metadata.create_all(self.engine)
            
            # إدراج البيانات الأساسية
            self._insert_default_data()
            
            print("تم تهيئة قاعدة البيانات بنجاح")
            
        except SQLAlchemyError as e:
            raise Exception(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
    
    def _insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        session = self.get_session()
        try:
            # التحقق من وجود بيانات أساسية
            if session.query(SystemSettings).count() == 0:
                # إدراج الإعدادات الافتراضية
                default_settings = [
                    SystemSettings(setting_key="company_name", setting_value="شركة الشحن المتقدمة", description="اسم الشركة"),
                    SystemSettings(setting_key="company_address", setting_value="المملكة العربية السعودية", description="عنوان الشركة"),
                    SystemSettings(setting_key="company_phone", setting_value="", description="هاتف الشركة"),
                    SystemSettings(setting_key="company_email", setting_value="", description="بريد الشركة الإلكتروني"),
                    SystemSettings(setting_key="default_currency", setting_value="SAR", description="العملة الافتراضية"),
                    SystemSettings(setting_key="fiscal_year_start", setting_value="01-01", description="بداية السنة المالية"),
                    SystemSettings(setting_key="backup_enabled", setting_value="true", description="تفعيل النسخ الاحتياطي"),
                ]
                
                for setting in default_settings:
                    session.add(setting)
            
            # إدراج العملات الافتراضية
            if session.query(Currency).count() == 0:
                default_currencies = [
                    Currency(code="SAR", name="ريال سعودي", symbol="ر.س", exchange_rate=1.0, is_default=True),
                    Currency(code="USD", name="دولار أمريكي", symbol="$", exchange_rate=3.75, is_default=False),
                    Currency(code="EUR", name="يورو", symbol="€", exchange_rate=4.1, is_default=False),
                ]
                
                for currency in default_currencies:
                    session.add(currency)
            
            # إدراج وحدات القياس الافتراضية
            if session.query(UnitOfMeasure).count() == 0:
                default_units = [
                    UnitOfMeasure(code="PCS", name="قطعة", description="وحدة العد"),
                    UnitOfMeasure(code="KG", name="كيلوجرام", description="وحدة الوزن"),
                    UnitOfMeasure(code="M", name="متر", description="وحدة الطول"),
                    UnitOfMeasure(code="L", name="لتر", description="وحدة الحجم"),
                    UnitOfMeasure(code="BOX", name="صندوق", description="وحدة التعبئة"),
                ]
                
                for unit in default_units:
                    session.add(unit)
            
            session.commit()
            
        except Exception as e:
            session.rollback()
            raise Exception(f"خطأ في إدراج البيانات الافتراضية: {str(e)}")
        finally:
            session.close()
    
    def get_session(self):
        """الحصول على جلسة عمل جديدة"""
        if self.Session is None:
            raise Exception("قاعدة البيانات غير مهيأة")
        return self.Session()
    
    def backup_database(self, backup_path=None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            if backup_path is None:
                backup_dir = Path("backups")
                backup_dir.mkdir(exist_ok=True)
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = backup_dir / f"shipmentpro_backup_{timestamp}.db"
            
            # نسخ قاعدة البيانات
            import shutil
            shutil.copy2(self.db_path, backup_path)
            
            return str(backup_path)
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
    
    def restore_database(self, backup_path):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                raise Exception("ملف النسخة الاحتياطية غير موجود")
            
            # إغلاق الاتصالات الحالية
            if self.engine:
                self.engine.dispose()
            
            # استعادة قاعدة البيانات
            import shutil
            shutil.copy2(backup_path, self.db_path)
            
            # إعادة إعداد الاتصال
            self._setup_database()
            
        except Exception as e:
            raise Exception(f"خطأ في استعادة قاعدة البيانات: {str(e)}")
    
    def close(self):
        """إغلاق اتصال قاعدة البيانات"""
        if self.engine:
            self.engine.dispose()
            self.engine = None
            self.Session = None
