# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية لنظام تتبع الشحنات
Main Shipments Tracking Interface
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                               QPushButton, QLabel, QFrame, QMessageBox, QGroupBox,
                               QTableWidget, QTableWidgetItem, QLineEdit, QComboBox,
                               QDateEdit, QTextEdit, QFormLayout, QGridLayout)
from PySide6.QtCore import Qt, QDate

from ...utils.arabic_support import ArabicSupport
from ...utils.styles import AppStyles


class ShipmentsMainWidget(QWidget):
    """الواجهة الرئيسية لنظام تتبع الشحنات"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النظام
        title = QLabel("نظام تتبع الشحنات")
        title.setStyleSheet("font-weight: bold; font-size: 18px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)

        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setLayoutDirection(Qt.RightToLeft)
        self.tab_widget.setStyleSheet(AppStyles.get_tab_style())

        # تبويب إدارة الشحنات
        shipments_tab = self.create_shipments_tab()
        self.tab_widget.addTab(shipments_tab, "إدارة الشحنات")

        # تبويب تتبع الشحنات
        tracking_tab = self.create_tracking_tab()
        self.tab_widget.addTab(tracking_tab, "تتبع الشحنات")

        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        self.tab_widget.addTab(reports_tab, "التقارير")

        main_layout.addWidget(self.tab_widget)
        
        # إضافة أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.StyledPanel)
        buttons_frame.setStyleSheet(AppStyles.get_frame_style())
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        
        # زر الخروج
        self.exit_btn = QPushButton("خروج")
        self.exit_btn.setStyleSheet(AppStyles.get_primary_button_style())
        self.exit_btn.clicked.connect(self.close_shipments)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.exit_btn)
        
        layout.addWidget(buttons_frame)

    def create_shipments_tab(self):
        """إنشاء تبويب إدارة الشحنات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة بيانات الشحنة
        shipment_data_group = QGroupBox("بيانات الشحنة الأساسية")
        shipment_data_group.setStyleSheet(AppStyles.get_frame_style())
        shipment_data_layout = QFormLayout(shipment_data_group)

        self.shipment_number = QLineEdit()
        self.shipment_number.setPlaceholderText("رقم الشحنة...")
        shipment_data_layout.addRow("رقم الشحنة:", self.shipment_number)

        self.shipment_date = QDateEdit()
        self.shipment_date.setDate(QDate.currentDate())
        self.shipment_date.setCalendarPopup(True)
        shipment_data_layout.addRow("تاريخ الشحنة:", self.shipment_date)

        self.origin_country = QComboBox()
        self.origin_country.addItems(["اختر البلد", "المملكة العربية السعودية", "الإمارات العربية المتحدة", "الكويت", "قطر", "البحرين", "عمان"])
        shipment_data_layout.addRow("بلد المنشأ:", self.origin_country)

        self.destination_country = QComboBox()
        self.destination_country.addItems(["اختر البلد", "المملكة العربية السعودية", "الإمارات العربية المتحدة", "الكويت", "قطر", "البحرين", "عمان"])
        shipment_data_layout.addRow("بلد الوجهة:", self.destination_country)

        self.shipment_status = QComboBox()
        self.shipment_status.addItems(["قيد التحضير", "تم الشحن", "في الطريق", "وصل للجمارك", "تم التخليص", "تم التسليم"])
        shipment_data_layout.addRow("حالة الشحنة:", self.shipment_status)

        layout.addWidget(shipment_data_group)

        # جدول الشحنات
        shipments_table_group = QGroupBox("قائمة الشحنات")
        shipments_table_group.setStyleSheet(AppStyles.get_frame_style())
        shipments_table_layout = QVBoxLayout(shipments_table_group)

        self.shipments_table = QTableWidget()
        self.shipments_table.setColumnCount(7)
        self.shipments_table.setHorizontalHeaderLabels([
            "رقم الشحنة", "التاريخ", "بلد المنشأ", "بلد الوجهة", "الحالة", "القيمة", "ملاحظات"
        ])

        # إضافة بيانات تجريبية
        self.shipments_table.setRowCount(3)
        shipments_data = [
            ["SH-001", "2024-01-15", "السعودية", "الإمارات", "تم التسليم", "15,000.00", "شحنة أجهزة"],
            ["SH-002", "2024-01-20", "الإمارات", "السعودية", "في الطريق", "8,500.00", "مواد مكتبية"],
            ["SH-003", "2024-01-25", "السعودية", "الكويت", "قيد التحضير", "22,000.00", "معدات طبية"]
        ]

        for row, data in enumerate(shipments_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 4:  # عمود الحالة
                    if value == "تم التسليم":
                        item.setBackground(Qt.green)
                    elif value == "في الطريق":
                        item.setBackground(Qt.yellow)
                    elif value == "قيد التحضير":
                        item.setBackground(Qt.red)
                self.shipments_table.setItem(row, col, item)

        shipments_table_layout.addWidget(self.shipments_table)
        layout.addWidget(shipments_table_group)

        return widget

    def create_tracking_tab(self):
        """إنشاء تبويب تتبع الشحنات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة البحث عن الشحنة
        search_group = QGroupBox("البحث عن الشحنة")
        search_group.setStyleSheet(AppStyles.get_frame_style())
        search_layout = QHBoxLayout(search_group)

        search_layout.addWidget(QLabel("رقم الشحنة:"))
        self.tracking_number = QLineEdit()
        self.tracking_number.setPlaceholderText("أدخل رقم الشحنة للتتبع...")
        search_layout.addWidget(self.tracking_number)

        track_btn = QPushButton("تتبع")
        track_btn.setStyleSheet(AppStyles.get_button_style())
        search_layout.addWidget(track_btn)

        layout.addWidget(search_group)

        # مجموعة تفاصيل التتبع
        tracking_details_group = QGroupBox("تفاصيل التتبع")
        tracking_details_group.setStyleSheet(AppStyles.get_frame_style())
        tracking_details_layout = QVBoxLayout(tracking_details_group)

        self.tracking_table = QTableWidget()
        self.tracking_table.setColumnCount(4)
        self.tracking_table.setHorizontalHeaderLabels([
            "التاريخ والوقت", "الموقع", "الحالة", "التفاصيل"
        ])

        # إضافة بيانات تجريبية للتتبع
        self.tracking_table.setRowCount(5)
        tracking_data = [
            ["2024-01-15 09:00", "الرياض - السعودية", "تم الاستلام", "تم استلام الشحنة من المرسل"],
            ["2024-01-15 14:30", "مطار الملك خالد", "في المطار", "وصلت الشحنة للمطار"],
            ["2024-01-15 18:45", "في الطائرة", "تم الشحن", "الشحنة على متن الطائرة"],
            ["2024-01-16 02:15", "مطار دبي", "وصلت للوجهة", "وصلت الشحنة لمطار الوجهة"],
            ["2024-01-16 10:30", "دبي - الإمارات", "تم التسليم", "تم تسليم الشحنة للمستلم"]
        ]

        for row, data in enumerate(tracking_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 2:  # عمود الحالة
                    if value == "تم التسليم":
                        item.setBackground(Qt.green)
                    elif value == "في الطائرة":
                        item.setBackground(Qt.yellow)
                self.tracking_table.setItem(row, col, item)

        tracking_details_layout.addWidget(self.tracking_table)
        layout.addWidget(tracking_details_group)

        return widget

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة أنواع التقارير
        reports_types_group = QGroupBox("أنواع التقارير")
        reports_types_group.setStyleSheet(AppStyles.get_frame_style())
        reports_types_layout = QGridLayout(reports_types_group)

        # تقرير الشحنات الشهرية
        monthly_report_btn = QPushButton("تقرير الشحنات الشهرية")
        monthly_report_btn.setStyleSheet(AppStyles.get_button_style())
        reports_types_layout.addWidget(monthly_report_btn, 0, 0)

        # تقرير الشحنات حسب الحالة
        status_report_btn = QPushButton("تقرير الشحنات حسب الحالة")
        status_report_btn.setStyleSheet(AppStyles.get_button_style())
        reports_types_layout.addWidget(status_report_btn, 0, 1)

        # تقرير الشحنات المتأخرة
        delayed_report_btn = QPushButton("تقرير الشحنات المتأخرة")
        delayed_report_btn.setStyleSheet(AppStyles.get_danger_button_style())
        reports_types_layout.addWidget(delayed_report_btn, 1, 0)

        # تقرير إحصائيات الشحن
        stats_report_btn = QPushButton("تقرير إحصائيات الشحن")
        stats_report_btn.setStyleSheet(AppStyles.get_success_button_style())
        reports_types_layout.addWidget(stats_report_btn, 1, 1)

        layout.addWidget(reports_types_group)

        # مجموعة معاينة التقرير
        preview_group = QGroupBox("معاينة التقرير")
        preview_group.setStyleSheet(AppStyles.get_frame_style())
        preview_layout = QVBoxLayout(preview_group)

        development_notice = QLabel("""
        📊 تقارير الشحنات - قيد التطوير

        سيتم تطوير التقارير التالية:
        • تقارير الشحنات الشهرية والسنوية
        • تقارير الأداء والإحصائيات
        • تقارير الشحنات المتأخرة والمشاكل
        • تصدير التقارير بتنسيقات PDF و Excel
        • رسوم بيانية تفاعلية
        """)
        development_notice.setAlignment(Qt.AlignCenter)
        development_notice.setStyleSheet("color: #7f8c8d; font-size: 14px; padding: 30px;")
        development_notice.setWordWrap(True)
        preview_layout.addWidget(development_notice)

        layout.addWidget(preview_group)

        return widget

    def close_shipments(self):
        """إغلاق نافذة الشحنات"""
        self.parent().show_module("welcome")
