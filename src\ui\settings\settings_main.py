# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية لنظام الإعدادات العامة
Main Settings Interface
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                               QPushButton, QLabel, QFrame, QMessageBox, QFormLayout,
                               QLineEdit, QTextEdit, QComboBox, QCheckBox, QSpinBox,
                               QDateEdit, QGroupBox, QGridLayout, QTableWidget,
                               QTableWidgetItem, QHeaderView, QScrollArea)
from PySide6.QtCore import Qt, QDate

from ...utils.arabic_support import ArabicSupport
from ...utils.styles import AppStyles


class SettingsMainWidget(QWidget):
    """الواجهة الرئيسية لنظام الإعدادات"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setLayoutDirection(Qt.RightToLeft)
        self.tab_widget.setStyleSheet(AppStyles.get_tab_style())
        
        # إضافة التبويبات
        self.add_tabs()
        
        main_layout.addWidget(self.tab_widget)
        
        # إضافة أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def add_tabs(self):
        """إضافة التبويبات"""
        # تبويب المتغيرات العامة
        system_vars_tab = self.create_system_variables_tab()
        self.tab_widget.addTab(system_vars_tab, "المتغيرات العامة")
        
        # تبويب السنة المالية
        fiscal_year_tab = self.create_fiscal_year_tab()
        self.tab_widget.addTab(fiscal_year_tab, "السنة المالية")
        
        # تبويب العملات
        currencies_tab = self.create_currencies_tab()
        self.tab_widget.addTab(currencies_tab, "العملات")
        
        # تبويب بيانات الشركة
        company_tab = self.create_company_tab()
        self.tab_widget.addTab(company_tab, "بيانات الشركة")
        
        # تبويب المستخدمين
        users_tab = self.create_users_tab()
        self.tab_widget.addTab(users_tab, "المستخدمين")
        
        # تبويب الصلاحيات
        permissions_tab = self.create_permissions_tab()
        self.tab_widget.addTab(permissions_tab, "الصلاحيات")
        
        # تبويب فتح سنة جديدة
        new_year_tab = self.create_new_year_tab()
        self.tab_widget.addTab(new_year_tab, "فتح سنة جديدة")
    
    def create_system_variables_tab(self):
        """إنشاء تبويب المتغيرات العامة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # عنوان التبويب
        title = QLabel("إعدادات المتغيرات العامة للبرنامج")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # مجموعة إعدادات النظام
        system_group = QGroupBox("إعدادات النظام الأساسية")
        system_group.setStyleSheet(AppStyles.get_frame_style())
        system_layout = QFormLayout(system_group)

        # حقول النظام
        self.auto_save_check = QCheckBox("حفظ تلقائي")
        self.auto_save_check.setChecked(True)
        system_layout.addRow("الحفظ التلقائي:", self.auto_save_check)

        self.auto_save_interval = QSpinBox()
        self.auto_save_interval.setRange(1, 60)
        self.auto_save_interval.setValue(5)
        self.auto_save_interval.setSuffix(" دقيقة")
        system_layout.addRow("فترة الحفظ التلقائي:", self.auto_save_interval)

        self.session_timeout = QSpinBox()
        self.session_timeout.setRange(30, 1440)
        self.session_timeout.setValue(480)
        self.session_timeout.setSuffix(" دقيقة")
        system_layout.addRow("انتهاء الجلسة:", self.session_timeout)

        self.max_login_attempts = QSpinBox()
        self.max_login_attempts.setRange(1, 10)
        self.max_login_attempts.setValue(3)
        system_layout.addRow("محاولات تسجيل الدخول:", self.max_login_attempts)

        scroll_layout.addWidget(system_group)

        # مجموعة إعدادات قاعدة البيانات
        db_group = QGroupBox("إعدادات قاعدة البيانات")
        db_group.setStyleSheet(AppStyles.get_frame_style())
        db_layout = QFormLayout(db_group)

        self.backup_enabled = QCheckBox("تفعيل النسخ الاحتياطية")
        self.backup_enabled.setChecked(True)
        db_layout.addRow("النسخ الاحتياطية:", self.backup_enabled)

        self.backup_interval = QSpinBox()
        self.backup_interval.setRange(1, 168)
        self.backup_interval.setValue(24)
        self.backup_interval.setSuffix(" ساعة")
        db_layout.addRow("فترة النسخ الاحتياطية:", self.backup_interval)

        self.max_backups = QSpinBox()
        self.max_backups.setRange(5, 100)
        self.max_backups.setValue(30)
        db_layout.addRow("عدد النسخ المحفوظة:", self.max_backups)

        scroll_layout.addWidget(db_group)

        # مجموعة إعدادات الواجهة
        ui_group = QGroupBox("إعدادات الواجهة")
        ui_group.setStyleSheet(AppStyles.get_frame_style())
        ui_layout = QFormLayout(ui_group)

        self.language_combo = QComboBox()
        self.language_combo.addItems(["العربية", "English"])
        ui_layout.addRow("اللغة:", self.language_combo)

        self.font_size = QSpinBox()
        self.font_size.setRange(8, 20)
        self.font_size.setValue(10)
        ui_layout.addRow("حجم الخط:", self.font_size)

        self.rtl_enabled = QCheckBox("تفعيل RTL")
        self.rtl_enabled.setChecked(True)
        ui_layout.addRow("اتجاه النص:", self.rtl_enabled)

        scroll_layout.addWidget(ui_group)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        return widget
    
    def create_fiscal_year_tab(self):
        """إنشاء تبويب السنة المالية"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        title = QLabel("إعداد السنة المالية")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # مجموعة السنة المالية الحالية
        current_year_group = QGroupBox("السنة المالية الحالية")
        current_year_group.setStyleSheet(AppStyles.get_frame_style())
        current_layout = QFormLayout(current_year_group)

        self.current_year_start = QDateEdit()
        self.current_year_start.setDate(QDate.currentDate().addDays(-QDate.currentDate().dayOfYear() + 1))
        self.current_year_start.setCalendarPopup(True)
        current_layout.addRow("تاريخ بداية السنة:", self.current_year_start)

        self.current_year_end = QDateEdit()
        self.current_year_end.setDate(QDate.currentDate().addDays(365 - QDate.currentDate().dayOfYear()))
        self.current_year_end.setCalendarPopup(True)
        current_layout.addRow("تاريخ نهاية السنة:", self.current_year_end)

        self.year_status = QComboBox()
        self.year_status.addItems(["مفتوحة", "مغلقة", "مؤقتة"])
        current_layout.addRow("حالة السنة:", self.year_status)

        layout.addWidget(current_year_group)

        # مجموعة إعدادات السنة المالية
        settings_group = QGroupBox("إعدادات السنة المالية")
        settings_group.setStyleSheet(AppStyles.get_frame_style())
        settings_layout = QFormLayout(settings_group)

        self.auto_close_year = QCheckBox("إغلاق تلقائي للسنة")
        settings_layout.addRow("الإغلاق التلقائي:", self.auto_close_year)

        self.allow_previous_year_edit = QCheckBox("السماح بتعديل السنة السابقة")
        settings_layout.addRow("تعديل السنة السابقة:", self.allow_previous_year_edit)

        self.fiscal_year_type = QComboBox()
        self.fiscal_year_type.addItems(["ميلادية", "هجرية", "مخصصة"])
        settings_layout.addRow("نوع السنة المالية:", self.fiscal_year_type)

        layout.addWidget(settings_group)

        # مجموعة السنوات السابقة
        previous_years_group = QGroupBox("السنوات المالية السابقة")
        previous_years_group.setStyleSheet(AppStyles.get_frame_style())
        previous_layout = QVBoxLayout(previous_years_group)

        self.years_table = QTableWidget()
        self.years_table.setColumnCount(4)
        self.years_table.setHorizontalHeaderLabels(["السنة", "تاريخ البداية", "تاريخ النهاية", "الحالة"])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.years_table)

        # إضافة بيانات تجريبية
        self.years_table.setRowCount(3)
        years_data = [
            ["2023", "01/01/2023", "31/12/2023", "مغلقة"],
            ["2022", "01/01/2022", "31/12/2022", "مغلقة"],
            ["2021", "01/01/2021", "31/12/2021", "مغلقة"]
        ]

        for row, data in enumerate(years_data):
            for col, value in enumerate(data):
                self.years_table.setItem(row, col, QTableWidgetItem(value))

        previous_layout.addWidget(self.years_table)
        layout.addWidget(previous_years_group)

        return widget
    
    def create_currencies_tab(self):
        """إنشاء تبويب العملات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        title = QLabel("تهيئة العملات")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # مجموعة إضافة عملة جديدة
        add_currency_group = QGroupBox("إضافة عملة جديدة")
        add_currency_group.setStyleSheet(AppStyles.get_frame_style())
        add_layout = QFormLayout(add_currency_group)

        self.currency_code = QLineEdit()
        self.currency_code.setPlaceholderText("مثال: USD, EUR, SAR")
        add_layout.addRow("رمز العملة:", self.currency_code)

        self.currency_name = QLineEdit()
        self.currency_name.setPlaceholderText("مثال: الريال السعودي")
        add_layout.addRow("اسم العملة:", self.currency_name)

        self.currency_symbol = QLineEdit()
        self.currency_symbol.setPlaceholderText("مثال: ر.س")
        add_layout.addRow("رمز العملة:", self.currency_symbol)

        self.exchange_rate = QLineEdit()
        self.exchange_rate.setPlaceholderText("1.00")
        add_layout.addRow("سعر الصرف:", self.exchange_rate)

        self.is_default_currency = QCheckBox("العملة الافتراضية")
        add_layout.addRow("افتراضية:", self.is_default_currency)

        self.currency_active = QCheckBox("عملة نشطة")
        self.currency_active.setChecked(True)
        add_layout.addRow("نشطة:", self.currency_active)

        # أزرار إدارة العملة
        currency_buttons_layout = QHBoxLayout()

        add_currency_btn = QPushButton("إضافة عملة")
        add_currency_btn.setStyleSheet(AppStyles.get_success_button_style())
        add_currency_btn.clicked.connect(self.add_currency)
        currency_buttons_layout.addWidget(add_currency_btn)

        update_currency_btn = QPushButton("تحديث عملة")
        update_currency_btn.setStyleSheet(AppStyles.get_button_style())
        update_currency_btn.clicked.connect(self.save_currency)
        currency_buttons_layout.addWidget(update_currency_btn)

        delete_currency_btn = QPushButton("حذف عملة")
        delete_currency_btn.setStyleSheet(AppStyles.get_danger_button_style())
        delete_currency_btn.clicked.connect(self.delete_currency)
        currency_buttons_layout.addWidget(delete_currency_btn)

        clear_form_btn = QPushButton("مسح النموذج")
        clear_form_btn.setStyleSheet(AppStyles.get_button_style())
        clear_form_btn.clicked.connect(self.clear_currency_form)
        currency_buttons_layout.addWidget(clear_form_btn)

        currency_buttons_layout.addStretch()
        add_layout.addRow(currency_buttons_layout)

        layout.addWidget(add_currency_group)

        # جدول العملات
        currencies_group = QGroupBox("العملات المسجلة")
        currencies_group.setStyleSheet(AppStyles.get_frame_style())
        currencies_layout = QVBoxLayout(currencies_group)

        self.currencies_table = QTableWidget()
        self.currencies_table.setColumnCount(6)
        self.currencies_table.setHorizontalHeaderLabels([
            "الرمز", "الاسم", "الرمز المختصر", "سعر الصرف", "افتراضية", "نشطة"
        ])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.currencies_table)

        # إضافة بيانات تجريبية
        self.currencies_table.setRowCount(4)
        currencies_data = [
            ["SAR", "الريال السعودي", "ر.س", "1.00", "نعم", "نعم"],
            ["USD", "الدولار الأمريكي", "$", "3.75", "لا", "نعم"],
            ["EUR", "اليورو", "€", "4.10", "لا", "نعم"],
            ["AED", "الدرهم الإماراتي", "د.إ", "1.02", "لا", "نعم"]
        ]

        for row, data in enumerate(currencies_data):
            for col, value in enumerate(data):
                self.currencies_table.setItem(row, col, QTableWidgetItem(value))

        # ربط النقر على الجدول بتحميل البيانات
        self.currencies_table.itemClicked.connect(self.load_currency_data)

        currencies_layout.addWidget(self.currencies_table)
        layout.addWidget(currencies_group)

        return widget
    
    def create_company_tab(self):
        """إنشاء تبويب بيانات الشركة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        title = QLabel("بيانات الشركة والفروع")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # مجموعة البيانات الأساسية
        basic_info_group = QGroupBox("البيانات الأساسية")
        basic_info_group.setStyleSheet(AppStyles.get_frame_style())
        basic_layout = QFormLayout(basic_info_group)

        self.company_name = QLineEdit()
        self.company_name.setPlaceholderText("اسم الشركة باللغة العربية")
        basic_layout.addRow("اسم الشركة:", self.company_name)

        self.company_name_en = QLineEdit()
        self.company_name_en.setPlaceholderText("Company Name in English")
        basic_layout.addRow("اسم الشركة (إنجليزي):", self.company_name_en)

        self.commercial_register = QLineEdit()
        self.commercial_register.setPlaceholderText("رقم السجل التجاري")
        basic_layout.addRow("السجل التجاري:", self.commercial_register)

        self.tax_number = QLineEdit()
        self.tax_number.setPlaceholderText("الرقم الضريبي")
        basic_layout.addRow("الرقم الضريبي:", self.tax_number)

        scroll_layout.addWidget(basic_info_group)

        # مجموعة معلومات الاتصال
        contact_group = QGroupBox("معلومات الاتصال")
        contact_group.setStyleSheet(AppStyles.get_frame_style())
        contact_layout = QFormLayout(contact_group)

        self.company_address = QTextEdit()
        self.company_address.setMaximumHeight(80)
        self.company_address.setPlaceholderText("العنوان الكامل للشركة")
        contact_layout.addRow("العنوان:", self.company_address)

        self.company_city = QLineEdit()
        self.company_city.setPlaceholderText("المدينة")
        contact_layout.addRow("المدينة:", self.company_city)

        self.company_country = QLineEdit()
        self.company_country.setPlaceholderText("الدولة")
        contact_layout.addRow("الدولة:", self.company_country)

        self.postal_code = QLineEdit()
        self.postal_code.setPlaceholderText("الرمز البريدي")
        contact_layout.addRow("الرمز البريدي:", self.postal_code)

        self.company_phone = QLineEdit()
        self.company_phone.setPlaceholderText("+966 XX XXX XXXX")
        contact_layout.addRow("الهاتف:", self.company_phone)

        self.company_fax = QLineEdit()
        self.company_fax.setPlaceholderText("+966 XX XXX XXXX")
        contact_layout.addRow("الفاكس:", self.company_fax)

        self.company_email = QLineEdit()
        self.company_email.setPlaceholderText("<EMAIL>")
        contact_layout.addRow("البريد الإلكتروني:", self.company_email)

        self.company_website = QLineEdit()
        self.company_website.setPlaceholderText("www.company.com")
        contact_layout.addRow("الموقع الإلكتروني:", self.company_website)

        scroll_layout.addWidget(contact_group)

        # مجموعة الشعار والهوية
        branding_group = QGroupBox("الشعار والهوية البصرية")
        branding_group.setStyleSheet(AppStyles.get_frame_style())
        branding_layout = QFormLayout(branding_group)

        logo_layout = QHBoxLayout()
        self.logo_path = QLineEdit()
        self.logo_path.setPlaceholderText("مسار ملف الشعار")
        logo_layout.addWidget(self.logo_path)

        browse_logo_btn = QPushButton("استعراض")
        browse_logo_btn.setStyleSheet(AppStyles.get_button_style())
        logo_layout.addWidget(browse_logo_btn)

        branding_layout.addRow("شعار الشركة:", logo_layout)

        scroll_layout.addWidget(branding_group)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        return widget
    
    def create_users_tab(self):
        """إنشاء تبويب المستخدمين"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        title = QLabel("بيانات المستخدمين")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # مجموعة إضافة مستخدم جديد
        add_user_group = QGroupBox("إضافة مستخدم جديد")
        add_user_group.setStyleSheet(AppStyles.get_frame_style())
        add_user_layout = QFormLayout(add_user_group)

        self.username = QLineEdit()
        self.username.setPlaceholderText("اسم المستخدم للدخول")
        add_user_layout.addRow("اسم المستخدم:", self.username)

        self.user_full_name = QLineEdit()
        self.user_full_name.setPlaceholderText("الاسم الكامل")
        add_user_layout.addRow("الاسم الكامل:", self.user_full_name)

        self.user_password = QLineEdit()
        self.user_password.setEchoMode(QLineEdit.Password)
        self.user_password.setPlaceholderText("كلمة المرور")
        add_user_layout.addRow("كلمة المرور:", self.user_password)

        self.user_email = QLineEdit()
        self.user_email.setPlaceholderText("البريد الإلكتروني")
        add_user_layout.addRow("البريد الإلكتروني:", self.user_email)

        self.user_phone = QLineEdit()
        self.user_phone.setPlaceholderText("رقم الهاتف")
        add_user_layout.addRow("الهاتف:", self.user_phone)

        self.is_admin = QCheckBox("مدير النظام")
        add_user_layout.addRow("صلاحيات الإدارة:", self.is_admin)

        self.user_active = QCheckBox("مستخدم نشط")
        self.user_active.setChecked(True)
        add_user_layout.addRow("حالة المستخدم:", self.user_active)

        # أزرار إدارة المستخدمين
        user_buttons_layout = QHBoxLayout()

        add_user_btn = QPushButton("إضافة مستخدم")
        add_user_btn.setStyleSheet(AppStyles.get_success_button_style())
        user_buttons_layout.addWidget(add_user_btn)

        update_user_btn = QPushButton("تحديث مستخدم")
        update_user_btn.setStyleSheet(AppStyles.get_button_style())
        user_buttons_layout.addWidget(update_user_btn)

        reset_password_btn = QPushButton("إعادة تعيين كلمة المرور")
        reset_password_btn.setStyleSheet(AppStyles.get_warning_button_style())
        user_buttons_layout.addWidget(reset_password_btn)

        delete_user_btn = QPushButton("حذف مستخدم")
        delete_user_btn.setStyleSheet(AppStyles.get_danger_button_style())
        user_buttons_layout.addWidget(delete_user_btn)

        user_buttons_layout.addStretch()
        add_user_layout.addRow(user_buttons_layout)

        layout.addWidget(add_user_group)

        # جدول المستخدمين
        users_group = QGroupBox("المستخدمون المسجلون")
        users_group.setStyleSheet(AppStyles.get_frame_style())
        users_layout = QVBoxLayout(users_group)

        self.users_table = QTableWidget()
        self.users_table.setColumnCount(7)
        self.users_table.setHorizontalHeaderLabels([
            "اسم المستخدم", "الاسم الكامل", "البريد الإلكتروني", "الهاتف",
            "مدير", "نشط", "آخر دخول"
        ])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.users_table)

        # إضافة بيانات تجريبية
        self.users_table.setRowCount(3)
        users_data = [
            ["admin", "مدير النظام", "<EMAIL>", "0501234567", "نعم", "نعم", "2024-01-15"],
            ["user1", "محمد أحمد", "<EMAIL>", "0507654321", "لا", "نعم", "2024-01-14"],
            ["user2", "فاطمة علي", "<EMAIL>", "0509876543", "لا", "نعم", "2024-01-13"]
        ]

        for row, data in enumerate(users_data):
            for col, value in enumerate(data):
                self.users_table.setItem(row, col, QTableWidgetItem(value))

        users_layout.addWidget(self.users_table)
        layout.addWidget(users_group)

        return widget
    
    def create_permissions_tab(self):
        """إنشاء تبويب الصلاحيات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        title = QLabel("صلاحيات المستخدمين")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()

        # قائمة المستخدمين
        users_group = QGroupBox("المستخدمون")
        users_group.setStyleSheet(AppStyles.get_frame_style())
        users_group.setMaximumWidth(250)
        users_layout = QVBoxLayout(users_group)

        self.permissions_users_table = QTableWidget()
        self.permissions_users_table.setColumnCount(2)
        self.permissions_users_table.setHorizontalHeaderLabels(["المستخدم", "النوع"])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.permissions_users_table)

        # إضافة بيانات المستخدمين
        self.permissions_users_table.setRowCount(3)
        users_permissions_data = [
            ["admin", "مدير"],
            ["user1", "مستخدم"],
            ["user2", "مستخدم"]
        ]

        for row, data in enumerate(users_permissions_data):
            for col, value in enumerate(data):
                self.permissions_users_table.setItem(row, col, QTableWidgetItem(value))

        users_layout.addWidget(self.permissions_users_table)
        content_layout.addWidget(users_group)

        # صلاحيات النظام
        permissions_group = QGroupBox("صلاحيات النظام")
        permissions_group.setStyleSheet(AppStyles.get_frame_style())
        permissions_layout = QVBoxLayout(permissions_group)

        # منطقة التمرير للصلاحيات
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # مجموعة صلاحيات الإعدادات
        settings_permissions = QGroupBox("صلاحيات الإعدادات")
        settings_perm_layout = QVBoxLayout(settings_permissions)

        self.perm_system_settings = QCheckBox("إدارة إعدادات النظام")
        self.perm_users_management = QCheckBox("إدارة المستخدمين")
        self.perm_company_data = QCheckBox("تعديل بيانات الشركة")
        self.perm_currencies = QCheckBox("إدارة العملات")

        settings_perm_layout.addWidget(self.perm_system_settings)
        settings_perm_layout.addWidget(self.perm_users_management)
        settings_perm_layout.addWidget(self.perm_company_data)
        settings_perm_layout.addWidget(self.perm_currencies)

        scroll_layout.addWidget(settings_permissions)

        # مجموعة صلاحيات الأصناف
        items_permissions = QGroupBox("صلاحيات الأصناف")
        items_perm_layout = QVBoxLayout(items_permissions)

        self.perm_items_add = QCheckBox("إضافة أصناف")
        self.perm_items_edit = QCheckBox("تعديل أصناف")
        self.perm_items_delete = QCheckBox("حذف أصناف")
        self.perm_items_view = QCheckBox("عرض الأصناف")

        items_perm_layout.addWidget(self.perm_items_add)
        items_perm_layout.addWidget(self.perm_items_edit)
        items_perm_layout.addWidget(self.perm_items_delete)
        items_perm_layout.addWidget(self.perm_items_view)

        scroll_layout.addWidget(items_permissions)

        # مجموعة صلاحيات الموردين
        suppliers_permissions = QGroupBox("صلاحيات الموردين")
        suppliers_perm_layout = QVBoxLayout(suppliers_permissions)

        self.perm_suppliers_add = QCheckBox("إضافة موردين")
        self.perm_suppliers_edit = QCheckBox("تعديل موردين")
        self.perm_suppliers_delete = QCheckBox("حذف موردين")
        self.perm_suppliers_view = QCheckBox("عرض الموردين")

        suppliers_perm_layout.addWidget(self.perm_suppliers_add)
        suppliers_perm_layout.addWidget(self.perm_suppliers_edit)
        suppliers_perm_layout.addWidget(self.perm_suppliers_delete)
        suppliers_perm_layout.addWidget(self.perm_suppliers_view)

        scroll_layout.addWidget(suppliers_permissions)

        # مجموعة صلاحيات التقارير
        reports_permissions = QGroupBox("صلاحيات التقارير")
        reports_perm_layout = QVBoxLayout(reports_permissions)

        self.perm_reports_view = QCheckBox("عرض التقارير")
        self.perm_reports_export = QCheckBox("تصدير التقارير")
        self.perm_reports_print = QCheckBox("طباعة التقارير")

        reports_perm_layout.addWidget(self.perm_reports_view)
        reports_perm_layout.addWidget(self.perm_reports_export)
        reports_perm_layout.addWidget(self.perm_reports_print)

        scroll_layout.addWidget(reports_permissions)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        permissions_layout.addWidget(scroll_area)

        # أزرار حفظ الصلاحيات
        permissions_buttons_layout = QHBoxLayout()

        save_permissions_btn = QPushButton("حفظ الصلاحيات")
        save_permissions_btn.setStyleSheet(AppStyles.get_success_button_style())
        permissions_buttons_layout.addWidget(save_permissions_btn)

        reset_permissions_btn = QPushButton("إعادة تعيين")
        reset_permissions_btn.setStyleSheet(AppStyles.get_warning_button_style())
        permissions_buttons_layout.addWidget(reset_permissions_btn)

        permissions_buttons_layout.addStretch()
        permissions_layout.addLayout(permissions_buttons_layout)

        content_layout.addWidget(permissions_group)
        layout.addLayout(content_layout)

        return widget
    
    def create_new_year_tab(self):
        """إنشاء تبويب فتح سنة جديدة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        title = QLabel("فتح سنة جديدة")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # مجموعة معلومات السنة الحالية
        current_info_group = QGroupBox("معلومات السنة الحالية")
        current_info_group.setStyleSheet(AppStyles.get_frame_style())
        current_info_layout = QFormLayout(current_info_group)

        current_year_label = QLabel("2024")
        current_year_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        current_info_layout.addRow("السنة الحالية:", current_year_label)

        current_start_label = QLabel("01/01/2024")
        current_info_layout.addRow("تاريخ البداية:", current_start_label)

        current_end_label = QLabel("31/12/2024")
        current_info_layout.addRow("تاريخ النهاية:", current_end_label)

        current_status_label = QLabel("مفتوحة")
        current_status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        current_info_layout.addRow("حالة السنة:", current_status_label)

        layout.addWidget(current_info_group)

        # مجموعة إعداد السنة الجديدة
        new_year_group = QGroupBox("إعداد السنة الجديدة")
        new_year_group.setStyleSheet(AppStyles.get_frame_style())
        new_year_layout = QFormLayout(new_year_group)

        self.new_year_number = QSpinBox()
        self.new_year_number.setRange(2024, 2050)
        self.new_year_number.setValue(2025)
        new_year_layout.addRow("رقم السنة الجديدة:", self.new_year_number)

        self.new_year_start_date = QDateEdit()
        self.new_year_start_date.setDate(QDate(2025, 1, 1))
        self.new_year_start_date.setCalendarPopup(True)
        new_year_layout.addRow("تاريخ بداية السنة:", self.new_year_start_date)

        self.new_year_end_date = QDateEdit()
        self.new_year_end_date.setDate(QDate(2025, 12, 31))
        self.new_year_end_date.setCalendarPopup(True)
        new_year_layout.addRow("تاريخ نهاية السنة:", self.new_year_end_date)

        layout.addWidget(new_year_group)

        # مجموعة خيارات الفتح
        options_group = QGroupBox("خيارات فتح السنة الجديدة")
        options_group.setStyleSheet(AppStyles.get_frame_style())
        options_layout = QVBoxLayout(options_group)

        self.close_current_year = QCheckBox("إغلاق السنة الحالية تلقائياً")
        self.close_current_year.setChecked(True)
        options_layout.addWidget(self.close_current_year)

        self.copy_settings = QCheckBox("نسخ إعدادات السنة الحالية")
        self.copy_settings.setChecked(True)
        options_layout.addWidget(self.copy_settings)

        self.copy_items = QCheckBox("نسخ بيانات الأصناف")
        self.copy_items.setChecked(True)
        options_layout.addWidget(self.copy_items)

        self.copy_suppliers = QCheckBox("نسخ بيانات الموردين")
        self.copy_suppliers.setChecked(True)
        options_layout.addWidget(self.copy_suppliers)

        self.create_backup = QCheckBox("إنشاء نسخة احتياطية قبل الفتح")
        self.create_backup.setChecked(True)
        options_layout.addWidget(self.create_backup)

        layout.addWidget(options_group)

        # تحذير مهم
        warning_label = QLabel("⚠️ تحذير: فتح سنة جديدة عملية مهمة ولا يمكن التراجع عنها. تأكد من إنشاء نسخة احتياطية أولاً.")
        warning_label.setStyleSheet("""
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        """)
        warning_label.setWordWrap(True)
        layout.addWidget(warning_label)

        # أزرار العمليات
        buttons_layout = QHBoxLayout()

        open_year_btn = QPushButton("فتح السنة الجديدة")
        open_year_btn.setStyleSheet(AppStyles.get_success_button_style())
        buttons_layout.addWidget(open_year_btn)

        preview_btn = QPushButton("معاينة التغييرات")
        preview_btn.setStyleSheet(AppStyles.get_button_style())
        buttons_layout.addWidget(preview_btn)

        backup_btn = QPushButton("إنشاء نسخة احتياطية")
        backup_btn.setStyleSheet(AppStyles.get_warning_button_style())
        buttons_layout.addWidget(backup_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        return widget
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.StyledPanel)
        buttons_frame.setStyleSheet(AppStyles.get_frame_style())
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        
        # الأزرار الأساسية
        self.add_btn = QPushButton("إضافة")
        self.add_btn.setStyleSheet(AppStyles.get_success_button_style())
        self.add_btn.clicked.connect(self.add_record)

        self.edit_btn = QPushButton("تعديل")
        self.edit_btn.setStyleSheet(AppStyles.get_button_style())
        self.edit_btn.clicked.connect(self.edit_record)

        self.save_btn = QPushButton("حفظ")
        self.save_btn.setStyleSheet(AppStyles.get_success_button_style())
        self.save_btn.clicked.connect(self.save_record)

        self.search_btn = QPushButton("بحث")
        self.search_btn.setStyleSheet(AppStyles.get_button_style())
        self.search_btn.clicked.connect(self.search_records)

        self.delete_btn = QPushButton("حذف")
        self.delete_btn.setStyleSheet(AppStyles.get_danger_button_style())
        self.delete_btn.clicked.connect(self.delete_record)

        self.exit_btn = QPushButton("خروج")
        self.exit_btn.setStyleSheet(AppStyles.get_primary_button_style())
        self.exit_btn.clicked.connect(self.close_settings)

        # إضافة الأزرار للتخطيط
        buttons_layout.addWidget(self.add_btn)
        buttons_layout.addWidget(self.edit_btn)
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.search_btn)
        buttons_layout.addWidget(self.delete_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.exit_btn)
        
        layout.addWidget(buttons_frame)
    
    def show_sub_module(self, module_name):
        """عرض وحدة فرعية معينة"""
        tab_mapping = {
            "system_variables": 0,
            "fiscal_year": 1,
            "currencies": 2,
            "company_data": 3,
            "users": 4,
            "permissions": 5,
            "new_year": 6
        }
        
        if module_name in tab_mapping:
            self.tab_widget.setCurrentIndex(tab_mapping[module_name])
    
    def add_record(self):
        """إضافة سجل جديد"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 2:  # تبويب العملات
            self.add_currency()
        elif current_tab == 1:  # تبويب السنة المالية
            self.add_fiscal_year()
        else:
            tab_name = self.tab_widget.tabText(current_tab)
            QMessageBox.information(self, "إضافة", f"إضافة سجل جديد في {tab_name}")

    def edit_record(self):
        """تعديل سجل"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 2:  # تبويب العملات
            self.edit_currency()
        elif current_tab == 1:  # تبويب السنة المالية
            self.edit_fiscal_year()
        else:
            tab_name = self.tab_widget.tabText(current_tab)
            QMessageBox.information(self, "تعديل", f"تعديل سجل في {tab_name}")

    def save_record(self):
        """حفظ السجل"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 2:  # تبويب العملات
            self.save_currency()
        elif current_tab == 1:  # تبويب السنة المالية
            self.save_fiscal_year()
        else:
            tab_name = self.tab_widget.tabText(current_tab)
            QMessageBox.information(self, "حفظ", f"تم حفظ البيانات في {tab_name} بنجاح")

    def search_records(self):
        """البحث في السجلات"""
        current_tab = self.tab_widget.currentIndex()
        tab_name = self.tab_widget.tabText(current_tab)
        QMessageBox.information(self, "بحث", f"البحث في {tab_name}")

    def delete_record(self):
        """حذف سجل"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 2:  # تبويب العملات
            self.delete_currency()
        elif current_tab == 1:  # تبويب السنة المالية
            self.delete_fiscal_year()
        else:
            tab_name = self.tab_widget.tabText(current_tab)
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل تريد حذف السجل المحدد من {tab_name}؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                QMessageBox.information(self, "حذف", f"تم حذف السجل من {tab_name}")

    def load_currency_data(self, item):
        """تحميل بيانات العملة المحددة في النموذج"""
        if item is None:
            return

        row = item.row()

        # تحميل البيانات من الجدول إلى النموذج
        self.currency_code.setText(self.currencies_table.item(row, 0).text())
        self.currency_name.setText(self.currencies_table.item(row, 1).text())
        self.currency_symbol.setText(self.currencies_table.item(row, 2).text())
        self.exchange_rate.setText(self.currencies_table.item(row, 3).text())

        # تحديد حالة الخانات
        is_default = self.currencies_table.item(row, 4).text() == "نعم"
        is_active = self.currencies_table.item(row, 5).text() == "نعم"

        self.is_default_currency.setChecked(is_default)
        self.currency_active.setChecked(is_active)

        # تحديد الصف في الجدول
        self.currencies_table.selectRow(row)

    def add_currency(self):
        """إضافة عملة جديدة"""
        # التحقق من صحة البيانات
        if not self.currency_code.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رمز العملة")
            return

        if not self.currency_name.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم العملة")
            return

        try:
            exchange_rate = float(self.exchange_rate.text() or "1.0")
        except ValueError:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال سعر صرف صحيح")
            return

        # إضافة صف جديد للجدول
        row_count = self.currencies_table.rowCount()
        self.currencies_table.insertRow(row_count)

        # إضافة البيانات للجدول
        self.currencies_table.setItem(row_count, 0, QTableWidgetItem(self.currency_code.text().strip().upper()))
        self.currencies_table.setItem(row_count, 1, QTableWidgetItem(self.currency_name.text().strip()))
        self.currencies_table.setItem(row_count, 2, QTableWidgetItem(self.currency_symbol.text().strip()))
        self.currencies_table.setItem(row_count, 3, QTableWidgetItem(f"{exchange_rate:.2f}"))
        self.currencies_table.setItem(row_count, 4, QTableWidgetItem("نعم" if self.is_default_currency.isChecked() else "لا"))
        self.currencies_table.setItem(row_count, 5, QTableWidgetItem("نعم" if self.currency_active.isChecked() else "لا"))

        # مسح النموذج
        self.clear_currency_form()

        QMessageBox.information(self, "نجح", "تم إضافة العملة بنجاح")

    def edit_currency(self):
        """تعديل عملة محددة"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عملة للتعديل")
            return

        # تحميل البيانات في النموذج
        self.currency_code.setText(self.currencies_table.item(current_row, 0).text())
        self.currency_name.setText(self.currencies_table.item(current_row, 1).text())
        self.currency_symbol.setText(self.currencies_table.item(current_row, 2).text())
        self.exchange_rate.setText(self.currencies_table.item(current_row, 3).text())
        self.is_default_currency.setChecked(self.currencies_table.item(current_row, 4).text() == "نعم")
        self.currency_active.setChecked(self.currencies_table.item(current_row, 5).text() == "نعم")

        QMessageBox.information(self, "تعديل", "تم تحميل بيانات العملة في النموذج. قم بالتعديل ثم اضغط حفظ")

    def save_currency(self):
        """حفظ تعديلات العملة"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            # إذا لم يكن هناك صف محدد، قم بإضافة عملة جديدة
            self.add_currency()
            return

        # التحقق من صحة البيانات
        if not self.currency_code.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رمز العملة")
            return

        if not self.currency_name.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم العملة")
            return

        try:
            exchange_rate = float(self.exchange_rate.text() or "1.0")
        except ValueError:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال سعر صرف صحيح")
            return

        # تحديث البيانات في الجدول
        self.currencies_table.setItem(current_row, 0, QTableWidgetItem(self.currency_code.text().strip().upper()))
        self.currencies_table.setItem(current_row, 1, QTableWidgetItem(self.currency_name.text().strip()))
        self.currencies_table.setItem(current_row, 2, QTableWidgetItem(self.currency_symbol.text().strip()))
        self.currencies_table.setItem(current_row, 3, QTableWidgetItem(f"{exchange_rate:.2f}"))
        self.currencies_table.setItem(current_row, 4, QTableWidgetItem("نعم" if self.is_default_currency.isChecked() else "لا"))
        self.currencies_table.setItem(current_row, 5, QTableWidgetItem("نعم" if self.currency_active.isChecked() else "لا"))

        # مسح النموذج
        self.clear_currency_form()

        QMessageBox.information(self, "نجح", "تم حفظ تعديلات العملة بنجاح")

    def delete_currency(self):
        """حذف عملة محددة"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عملة للحذف")
            return

        currency_name = self.currencies_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل تريد حذف العملة '{currency_name}'؟\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.currencies_table.removeRow(current_row)
            QMessageBox.information(self, "نجح", f"تم حذف العملة '{currency_name}' بنجاح")

    def clear_currency_form(self):
        """مسح نموذج العملة"""
        self.currency_code.clear()
        self.currency_name.clear()
        self.currency_symbol.clear()
        self.exchange_rate.clear()
        self.is_default_currency.setChecked(False)
        self.currency_active.setChecked(True)

    def add_fiscal_year(self):
        """إضافة سنة مالية جديدة"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة السنة المالية قيد التطوير")

    def edit_fiscal_year(self):
        """تعديل سنة مالية"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل السنة المالية قيد التطوير")

    def save_fiscal_year(self):
        """حفظ السنة المالية"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حفظ السنة المالية قيد التطوير")

    def delete_fiscal_year(self):
        """حذف سنة مالية"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حذف السنة المالية قيد التطوير")

    def close_settings(self):
        """إغلاق نافذة الإعدادات"""
        if hasattr(self, 'main_window'):
            # إذا كانت النافذة مفتوحة في وضع ملء الشاشة
            self.window().close()
        else:
            # إذا كانت النافذة مدمجة في النافذة الرئيسية
            self.parent().show_module("welcome")
