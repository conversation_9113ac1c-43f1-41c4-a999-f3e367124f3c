@echo off
echo تشغيل نظام إدارة الشحنات المتكامل - ShipmentPro
echo Starting ShipmentPro System...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo Error: Python is not installed
    pause
    exit /b 1
)

REM التحقق من وجود المتطلبات
echo التحقق من المتطلبات...
echo Checking requirements...
pip show PySide6 >nul 2>&1
if errorlevel 1 (
    echo تثبيت المتطلبات...
    echo Installing requirements...
    pip install -r requirements.txt
)

REM تشغيل التطبيق
echo تشغيل التطبيق...
echo Starting application...
python main.py

REM في حالة إغلاق التطبيق
echo.
echo تم إغلاق التطبيق
echo Application closed
pause
