"""
شاشة إدارة الصلاحيات
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QLineEdit, QComboBox, QCheckBox,
                               QGroupBox, QPushButton, QFrame, QTreeWidget,
                               QTreeWidgetItem, QMessageBox)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from src.ui.styles.app_styles import AppStyles
from src.utils.arabic_support import ArabicSupport


class PermissionsWidget(QWidget):
    """شاشة إدارة الصلاحيات"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الشاشة
        title = QLabel("إدارة الصلاحيات والأدوار")
        title.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                font-size: 18px; 
                color: #2c3e50; 
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()
        
        # الجانب الأيسر - اختيار المستخدم والدور
        self.create_selection_section(content_layout)
        
        # الجانب الأيمن - شجرة الصلاحيات
        self.create_permissions_tree_section(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def create_selection_section(self, layout):
        """إنشاء قسم الاختيار"""
        selection_frame = QFrame()
        selection_frame.setMaximumWidth(300)
        selection_layout = QVBoxLayout(selection_frame)
        
        # مجموعة اختيار المستخدم
        user_group = QGroupBox("اختيار المستخدم")
        user_group.setStyleSheet(AppStyles.get_frame_style())
        user_layout = QFormLayout(user_group)
        
        self.user_combo = QComboBox()
        self.user_combo.addItems(["admin - مدير النظام", "manager1 - أحمد محمد", "user1 - فاطمة علي"])
        self.user_combo.currentTextChanged.connect(self.load_user_permissions)
        user_layout.addRow("المستخدم:", self.user_combo)
        
        self.role_combo = QComboBox()
        self.role_combo.addItems(["مدير النظام", "مدير", "مستخدم", "مستخدم محدود"])
        user_layout.addRow("الدور:", self.role_combo)
        
        selection_layout.addWidget(user_group)
        
        # مجموعة الإعدادات السريعة
        quick_group = QGroupBox("إعدادات سريعة")
        quick_group.setStyleSheet(AppStyles.get_frame_style())
        quick_layout = QVBoxLayout(quick_group)
        
        grant_all_btn = QPushButton("منح جميع الصلاحيات")
        grant_all_btn.clicked.connect(self.grant_all_permissions)
        grant_all_btn.setStyleSheet(AppStyles.get_button_style("success"))
        quick_layout.addWidget(grant_all_btn)
        
        revoke_all_btn = QPushButton("إلغاء جميع الصلاحيات")
        revoke_all_btn.clicked.connect(self.revoke_all_permissions)
        revoke_all_btn.setStyleSheet(AppStyles.get_button_style("danger"))
        quick_layout.addWidget(revoke_all_btn)
        
        copy_permissions_btn = QPushButton("نسخ الصلاحيات من مستخدم آخر")
        copy_permissions_btn.clicked.connect(self.copy_permissions)
        copy_permissions_btn.setStyleSheet(AppStyles.get_button_style("info"))
        quick_layout.addWidget(copy_permissions_btn)
        
        selection_layout.addWidget(quick_group)
        selection_layout.addStretch()
        
        layout.addWidget(selection_frame)
    
    def create_permissions_tree_section(self, layout):
        """إنشاء قسم شجرة الصلاحيات"""
        tree_frame = QFrame()
        tree_layout = QVBoxLayout(tree_frame)
        
        # عنوان الشجرة
        tree_title = QLabel("الصلاحيات التفصيلية")
        tree_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        tree_layout.addWidget(tree_title)
        
        # شجرة الصلاحيات
        self.permissions_tree = QTreeWidget()
        self.permissions_tree.setHeaderLabels(["الوحدة/الصلاحية", "الحالة"])
        
        # إضافة الصلاحيات
        self.setup_permissions_tree()
        
        tree_layout.addWidget(self.permissions_tree)
        layout.addWidget(tree_frame)
    
    def setup_permissions_tree(self):
        """إعداد شجرة الصلاحيات"""
        # الإعدادات العامة
        settings_item = QTreeWidgetItem(self.permissions_tree, ["الإعدادات العامة"])
        settings_item.setExpanded(True)
        
        # صلاحيات الإعدادات
        settings_permissions = [
            "عرض المتغيرات العامة", "تعديل المتغيرات العامة",
            "إدارة السنة المالية", "إدارة العملات",
            "إدارة بيانات الشركة", "إدارة المستخدمين"
        ]
        
        for perm in settings_permissions:
            perm_item = QTreeWidgetItem(settings_item, [perm])
            checkbox = QCheckBox()
            self.permissions_tree.setItemWidget(perm_item, 1, checkbox)
        
        # إدارة الأصناف
        items_item = QTreeWidgetItem(self.permissions_tree, ["إدارة الأصناف"])
        items_item.setExpanded(True)
        
        items_permissions = [
            "عرض الوحدات", "إضافة وحدة", "تعديل وحدة", "حذف وحدة",
            "عرض مجموعات الأصناف", "إدارة مجموعات الأصناف",
            "عرض بيانات الأصناف", "إضافة صنف", "تعديل صنف", "حذف صنف"
        ]
        
        for perm in items_permissions:
            perm_item = QTreeWidgetItem(items_item, [perm])
            checkbox = QCheckBox()
            self.permissions_tree.setItemWidget(perm_item, 1, checkbox)
        
        # إدارة الموردين
        suppliers_item = QTreeWidgetItem(self.permissions_tree, ["إدارة الموردين"])
        suppliers_item.setExpanded(True)
        
        suppliers_permissions = [
            "عرض بيانات الموردين", "إضافة مورد", "تعديل مورد", "حذف مورد",
            "عرض معاملات الموردين", "إدارة معاملات الموردين",
            "عرض تقارير الموردين", "طباعة تقارير الموردين"
        ]
        
        for perm in suppliers_permissions:
            perm_item = QTreeWidgetItem(suppliers_item, [perm])
            checkbox = QCheckBox()
            self.permissions_tree.setItemWidget(perm_item, 1, checkbox)
        
        # تتبع الشحنات
        shipments_item = QTreeWidgetItem(self.permissions_tree, ["تتبع الشحنات"])
        shipments_item.setExpanded(True)
        
        shipments_permissions = [
            "عرض الشحنات", "إضافة شحنة", "تعديل شحنة", "حذف شحنة",
            "تتبع الشحنات", "تحديث حالة الشحنة"
        ]
        
        for perm in shipments_permissions:
            perm_item = QTreeWidgetItem(shipments_item, [perm])
            checkbox = QCheckBox()
            self.permissions_tree.setItemWidget(perm_item, 1, checkbox)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر حفظ الصلاحيات
        save_btn = QPushButton("حفظ الصلاحيات")
        save_btn.setIcon(QIcon("assets/icons/save.png"))
        save_btn.clicked.connect(self.save_permissions)
        save_btn.setStyleSheet(AppStyles.get_button_style("success"))
        buttons_layout.addWidget(save_btn)
        
        # زر تصدير الصلاحيات
        export_btn = QPushButton("تصدير الصلاحيات")
        export_btn.setIcon(QIcon("assets/icons/export.png"))
        export_btn.clicked.connect(self.export_permissions)
        export_btn.setStyleSheet(AppStyles.get_button_style("info"))
        buttons_layout.addWidget(export_btn)
        
        # زر استيراد الصلاحيات
        import_btn = QPushButton("استيراد الصلاحيات")
        import_btn.setIcon(QIcon("assets/icons/import.png"))
        import_btn.clicked.connect(self.import_permissions)
        import_btn.setStyleSheet(AppStyles.get_button_style("warning"))
        buttons_layout.addWidget(import_btn)
        
        buttons_layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setIcon(QIcon("assets/icons/close.png"))
        close_btn.clicked.connect(self.close_screen)
        close_btn.setStyleSheet(AppStyles.get_button_style("secondary"))
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)
    
    def load_user_permissions(self):
        """تحميل صلاحيات المستخدم"""
        # تحميل الصلاحيات الحالية للمستخدم المختار
        pass
    
    def grant_all_permissions(self):
        """منح جميع الصلاحيات"""
        for i in range(self.permissions_tree.topLevelItemCount()):
            top_item = self.permissions_tree.topLevelItem(i)
            for j in range(top_item.childCount()):
                child_item = top_item.child(j)
                checkbox = self.permissions_tree.itemWidget(child_item, 1)
                if checkbox:
                    checkbox.setChecked(True)
        
        QMessageBox.information(self, "منح الصلاحيات", "تم منح جميع الصلاحيات!")
    
    def revoke_all_permissions(self):
        """إلغاء جميع الصلاحيات"""
        for i in range(self.permissions_tree.topLevelItemCount()):
            top_item = self.permissions_tree.topLevelItem(i)
            for j in range(top_item.childCount()):
                child_item = top_item.child(j)
                checkbox = self.permissions_tree.itemWidget(child_item, 1)
                if checkbox:
                    checkbox.setChecked(False)
        
        QMessageBox.information(self, "إلغاء الصلاحيات", "تم إلغاء جميع الصلاحيات!")
    
    def copy_permissions(self):
        """نسخ الصلاحيات من مستخدم آخر"""
        QMessageBox.information(self, "نسخ الصلاحيات", "سيتم تطبيق هذه الميزة قريباً...")
    
    def save_permissions(self):
        """حفظ الصلاحيات"""
        try:
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ الصلاحيات بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ الصلاحيات: {str(e)}")
    
    def export_permissions(self):
        """تصدير الصلاحيات"""
        QMessageBox.information(self, "تصدير", "تم تصدير الصلاحيات بنجاح!")
    
    def import_permissions(self):
        """استيراد الصلاحيات"""
        QMessageBox.information(self, "استيراد", "تم استيراد الصلاحيات بنجاح!")
    
    def close_screen(self):
        """إغلاق الشاشة"""
        if hasattr(self, 'main_window'):
            self.window().close()
        else:
            self.parent().close()
