"""
شاشة إدارة الوحدات
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QLineEdit, QComboBox, QCheckBox,
                               QGroupBox, QPushButton, QFrame,
                               QMessageBox, QTableWidget, QTableWidgetItem)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from src.ui.styles.app_styles import AppStyles
from src.utils.arabic_support import ArabicSupport


class UnitsWidget(QWidget):
    """شاشة إدارة وحدات القياس"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الشاشة
        title = QLabel("إدارة وحدات القياس")
        title.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                font-size: 18px; 
                color: #2c3e50; 
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()
        
        # الجانب الأيسر - نموذج البيانات
        self.create_form_section(content_layout)
        
        # الجانب الأيمن - جدول الوحدات
        self.create_table_section(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def create_form_section(self, layout):
        """إنشاء قسم النموذج"""
        form_frame = QFrame()
        form_frame.setMaximumWidth(400)
        form_layout = QVBoxLayout(form_frame)
        
        # مجموعة بيانات الوحدة
        unit_group = QGroupBox("بيانات الوحدة")
        unit_group.setStyleSheet(AppStyles.get_frame_style())
        unit_layout = QFormLayout(unit_group)
        
        self.unit_code = QLineEdit()
        self.unit_code.setPlaceholderText("رمز الوحدة")
        unit_layout.addRow("رمز الوحدة:", self.unit_code)
        
        self.unit_name_ar = QLineEdit()
        self.unit_name_ar.setPlaceholderText("اسم الوحدة بالعربية")
        unit_layout.addRow("الاسم (عربي):", self.unit_name_ar)
        
        self.unit_name_en = QLineEdit()
        self.unit_name_en.setPlaceholderText("Unit Name in English")
        unit_layout.addRow("الاسم (إنجليزي):", self.unit_name_en)
        
        self.unit_symbol = QLineEdit()
        self.unit_symbol.setPlaceholderText("رمز الوحدة (كجم، متر، لتر)")
        unit_layout.addRow("الرمز:", self.unit_symbol)
        
        self.unit_type = QComboBox()
        self.unit_type.addItems(["وزن", "طول", "حجم", "مساحة", "عدد", "أخرى"])
        unit_layout.addRow("نوع الوحدة:", self.unit_type)
        
        self.is_base_unit = QCheckBox("وحدة أساسية")
        unit_layout.addRow("النوع:", self.is_base_unit)
        
        self.is_active = QCheckBox("وحدة نشطة")
        self.is_active.setChecked(True)
        unit_layout.addRow("الحالة:", self.is_active)
        
        form_layout.addWidget(unit_group)
        
        # مجموعة التحويلات
        conversion_group = QGroupBox("تحويلات الوحدة")
        conversion_group.setStyleSheet(AppStyles.get_frame_style())
        conversion_layout = QFormLayout(conversion_group)
        
        self.base_unit = QComboBox()
        self.base_unit.addItems(["كيلوجرام", "متر", "لتر", "قطعة"])
        conversion_layout.addRow("الوحدة الأساسية:", self.base_unit)
        
        self.conversion_factor = QLineEdit()
        self.conversion_factor.setPlaceholderText("معامل التحويل")
        self.conversion_factor.setText("1.0")
        conversion_layout.addRow("معامل التحويل:", self.conversion_factor)
        
        self.conversion_formula = QLineEdit()
        self.conversion_formula.setPlaceholderText("صيغة التحويل")
        conversion_layout.addRow("صيغة التحويل:", self.conversion_formula)
        
        form_layout.addWidget(conversion_group)
        form_layout.addStretch()
        
        layout.addWidget(form_frame)
    
    def create_table_section(self, layout):
        """إنشاء قسم الجدول"""
        table_frame = QFrame()
        table_layout = QVBoxLayout(table_frame)
        
        # عنوان الجدول
        table_title = QLabel("وحدات القياس المسجلة")
        table_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        table_layout.addWidget(table_title)
        
        # الجدول
        self.units_table = QTableWidget()
        self.units_table.setColumnCount(7)
        self.units_table.setHorizontalHeaderLabels([
            "الرمز", "الاسم العربي", "الاسم الإنجليزي", "الرمز", "النوع", "معامل التحويل", "الحالة"
        ])
        
        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.units_table)
        
        # إضافة بيانات تجريبية
        self.units_table.setRowCount(8)
        units_data = [
            ["KG", "كيلوجرام", "Kilogram", "كجم", "وزن", "1.0", "نشطة"],
            ["G", "جرام", "Gram", "جم", "وزن", "0.001", "نشطة"],
            ["TON", "طن", "Ton", "طن", "وزن", "1000.0", "نشطة"],
            ["M", "متر", "Meter", "م", "طول", "1.0", "نشطة"],
            ["CM", "سنتيمتر", "Centimeter", "سم", "طول", "0.01", "نشطة"],
            ["L", "لتر", "Liter", "لتر", "حجم", "1.0", "نشطة"],
            ["ML", "مليلتر", "Milliliter", "مل", "حجم", "0.001", "نشطة"],
            ["PCS", "قطعة", "Piece", "قطعة", "عدد", "1.0", "نشطة"]
        ]
        
        for row, data in enumerate(units_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 4:  # عمود النوع
                    if value == "وزن":
                        item.setBackground(AppStyles.get_color("primary_light"))
                    elif value == "طول":
                        item.setBackground(AppStyles.get_color("info_light"))
                    elif value == "حجم":
                        item.setBackground(AppStyles.get_color("warning_light"))
                    else:
                        item.setBackground(AppStyles.get_color("secondary_light"))
                elif col == 6:  # عمود الحالة
                    if value == "نشطة":
                        item.setBackground(AppStyles.get_color("success_light"))
                    else:
                        item.setBackground(AppStyles.get_color("secondary_light"))
                
                self.units_table.setItem(row, col, item)
        
        table_layout.addWidget(self.units_table)
        layout.addWidget(table_frame)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر إضافة
        add_btn = QPushButton("إضافة وحدة")
        add_btn.setIcon(QIcon("assets/icons/add.png"))
        add_btn.clicked.connect(self.add_unit)
        add_btn.setStyleSheet(AppStyles.get_button_style("primary"))
        buttons_layout.addWidget(add_btn)
        
        # زر تعديل
        edit_btn = QPushButton("تعديل")
        edit_btn.setIcon(QIcon("assets/icons/edit.png"))
        edit_btn.clicked.connect(self.edit_unit)
        edit_btn.setStyleSheet(AppStyles.get_button_style("info"))
        buttons_layout.addWidget(edit_btn)
        
        # زر حفظ
        save_btn = QPushButton("حفظ")
        save_btn.setIcon(QIcon("assets/icons/save.png"))
        save_btn.clicked.connect(self.save_unit)
        save_btn.setStyleSheet(AppStyles.get_button_style("success"))
        buttons_layout.addWidget(save_btn)
        
        # زر حذف
        delete_btn = QPushButton("حذف")
        delete_btn.setIcon(QIcon("assets/icons/delete.png"))
        delete_btn.clicked.connect(self.delete_unit)
        delete_btn.setStyleSheet(AppStyles.get_button_style("danger"))
        buttons_layout.addWidget(delete_btn)
        
        # زر اختبار التحويل
        test_conversion_btn = QPushButton("اختبار التحويل")
        test_conversion_btn.setIcon(QIcon("assets/icons/calculator.png"))
        test_conversion_btn.clicked.connect(self.test_conversion)
        test_conversion_btn.setStyleSheet(AppStyles.get_button_style("warning"))
        buttons_layout.addWidget(test_conversion_btn)
        
        buttons_layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setIcon(QIcon("assets/icons/close.png"))
        close_btn.clicked.connect(self.close_screen)
        close_btn.setStyleSheet(AppStyles.get_button_style("secondary"))
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)
    
    def add_unit(self):
        """إضافة وحدة جديدة"""
        if not self.unit_code.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز الوحدة")
            return
        
        if not self.unit_name_ar.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الوحدة بالعربية")
            return
        
        QMessageBox.information(self, "نجح", "تم إضافة الوحدة بنجاح!")
        self.clear_form()
    
    def edit_unit(self):
        """تعديل وحدة"""
        current_row = self.units_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار وحدة للتعديل")
            return
        
        self.load_unit_data(current_row)
    
    def save_unit(self):
        """حفظ الوحدة"""
        try:
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ بيانات الوحدة بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
    
    def delete_unit(self):
        """حذف وحدة"""
        current_row = self.units_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار وحدة للحذف")
            return
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   "هل تريد حذف الوحدة المختارة؟")
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الحذف", "تم حذف الوحدة بنجاح!")
    
    def test_conversion(self):
        """اختبار تحويل الوحدات"""
        QMessageBox.information(self, "اختبار التحويل", 
                              "مثال: 1 كيلوجرام = 1000 جرام\n"
                              "1 متر = 100 سنتيمتر\n"
                              "1 لتر = 1000 مليلتر")
    
    def load_unit_data(self, row):
        """تحميل بيانات الوحدة"""
        self.unit_code.setText(self.units_table.item(row, 0).text())
        self.unit_name_ar.setText(self.units_table.item(row, 1).text())
        self.unit_name_en.setText(self.units_table.item(row, 2).text())
        self.unit_symbol.setText(self.units_table.item(row, 3).text())
        self.conversion_factor.setText(self.units_table.item(row, 5).text())
    
    def clear_form(self):
        """مسح النموذج"""
        self.unit_code.clear()
        self.unit_name_ar.clear()
        self.unit_name_en.clear()
        self.unit_symbol.clear()
        self.conversion_factor.setText("1.0")
        self.conversion_formula.clear()
        self.is_base_unit.setChecked(False)
        self.is_active.setChecked(True)
    
    def close_screen(self):
        """إغلاق الشاشة"""
        if hasattr(self, 'main_window'):
            self.window().close()
        else:
            self.parent().close()
