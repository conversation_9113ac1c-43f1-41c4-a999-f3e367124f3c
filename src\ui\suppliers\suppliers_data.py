"""
شاشة إدارة بيانات الموردين
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QLineEdit, QComboBox, QCheckBox, QTextEdit,
                               QGroupBox, QPushButton, QFrame, QTabWidget,
                               QMessageBox, QTableWidget, QTableWidgetItem)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from src.utils.styles import AppStyles
from src.utils.arabic_support import ArabicSupport


class SuppliersDataWidget(QWidget):
    """شاشة إدارة بيانات الموردين"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الشاشة
        title = QLabel("إدارة بيانات الموردين")
        title.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                font-size: 18px; 
                color: #2c3e50; 
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()
        
        # الجانب الأيسر - نموذج البيانات
        self.create_form_section(content_layout)
        
        # الجانب الأيمن - جدول الموردين
        self.create_table_section(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def create_form_section(self, layout):
        """إنشاء قسم النموذج"""
        form_frame = QFrame()
        form_frame.setMaximumWidth(450)
        form_layout = QVBoxLayout(form_frame)
        
        # تبويبات البيانات
        tabs = QTabWidget()
        
        # تبويب البيانات الأساسية
        basic_tab = QWidget()
        self.create_basic_info_tab(basic_tab)
        tabs.addTab(basic_tab, "البيانات الأساسية")
        
        # تبويب معلومات الاتصال
        contact_tab = QWidget()
        self.create_contact_tab(contact_tab)
        tabs.addTab(contact_tab, "معلومات الاتصال")
        
        # تبويب البيانات المالية
        financial_tab = QWidget()
        self.create_financial_tab(financial_tab)
        tabs.addTab(financial_tab, "البيانات المالية")
        
        form_layout.addWidget(tabs)
        layout.addWidget(form_frame)
    
    def create_basic_info_tab(self, tab):
        """إنشاء تبويب البيانات الأساسية"""
        layout = QVBoxLayout(tab)
        
        # مجموعة البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_group.setStyleSheet(AppStyles.get_frame_style())
        basic_layout = QFormLayout(basic_group)
        
        self.supplier_code = QLineEdit()
        self.supplier_code.setPlaceholderText("رمز المورد")
        basic_layout.addRow("رمز المورد:", self.supplier_code)
        
        self.supplier_name_ar = QLineEdit()
        self.supplier_name_ar.setPlaceholderText("اسم المورد بالعربية")
        basic_layout.addRow("الاسم (عربي):", self.supplier_name_ar)
        
        self.supplier_name_en = QLineEdit()
        self.supplier_name_en.setPlaceholderText("Supplier Name in English")
        basic_layout.addRow("الاسم (إنجليزي):", self.supplier_name_en)
        
        self.supplier_type = QComboBox()
        self.supplier_type.addItems(["شركة", "مؤسسة فردية", "مصنع", "مستورد", "موزع"])
        basic_layout.addRow("نوع المورد:", self.supplier_type)
        
        self.category = QComboBox()
        self.category.addItems(["إلكترونيات", "ملابس", "أغذية", "مواد بناء", "أخرى"])
        basic_layout.addRow("الفئة:", self.category)
        
        self.country = QComboBox()
        self.country.addItems(["السعودية", "الإمارات", "الكويت", "قطر", "البحرين", "عمان", "أخرى"])
        basic_layout.addRow("البلد:", self.country)
        
        layout.addWidget(basic_group)
        
        # مجموعة البيانات القانونية
        legal_group = QGroupBox("البيانات القانونية")
        legal_group.setStyleSheet(AppStyles.get_frame_style())
        legal_layout = QFormLayout(legal_group)
        
        self.commercial_register = QLineEdit()
        self.commercial_register.setPlaceholderText("رقم السجل التجاري")
        legal_layout.addRow("السجل التجاري:", self.commercial_register)
        
        self.tax_number = QLineEdit()
        self.tax_number.setPlaceholderText("الرقم الضريبي")
        legal_layout.addRow("الرقم الضريبي:", self.tax_number)
        
        self.license_number = QLineEdit()
        self.license_number.setPlaceholderText("رقم الترخيص")
        legal_layout.addRow("رقم الترخيص:", self.license_number)
        
        layout.addWidget(legal_group)
        layout.addStretch()
    
    def create_contact_tab(self, tab):
        """إنشاء تبويب معلومات الاتصال"""
        layout = QVBoxLayout(tab)
        
        # مجموعة العنوان
        address_group = QGroupBox("العنوان")
        address_group.setStyleSheet(AppStyles.get_frame_style())
        address_layout = QFormLayout(address_group)
        
        self.address = QTextEdit()
        self.address.setMaximumHeight(80)
        self.address.setPlaceholderText("العنوان التفصيلي")
        address_layout.addRow("العنوان:", self.address)
        
        self.city = QLineEdit()
        self.city.setPlaceholderText("المدينة")
        address_layout.addRow("المدينة:", self.city)
        
        self.postal_code = QLineEdit()
        self.postal_code.setPlaceholderText("الرمز البريدي")
        address_layout.addRow("الرمز البريدي:", self.postal_code)
        
        layout.addWidget(address_group)
        
        # مجموعة الاتصال
        contact_group = QGroupBox("معلومات الاتصال")
        contact_group.setStyleSheet(AppStyles.get_frame_style())
        contact_layout = QFormLayout(contact_group)
        
        self.phone = QLineEdit()
        self.phone.setPlaceholderText("رقم الهاتف")
        contact_layout.addRow("الهاتف:", self.phone)
        
        self.mobile = QLineEdit()
        self.mobile.setPlaceholderText("رقم الجوال")
        contact_layout.addRow("الجوال:", self.mobile)
        
        self.fax = QLineEdit()
        self.fax.setPlaceholderText("رقم الفاكس")
        contact_layout.addRow("الفاكس:", self.fax)
        
        self.email = QLineEdit()
        self.email.setPlaceholderText("البريد الإلكتروني")
        contact_layout.addRow("البريد الإلكتروني:", self.email)
        
        self.website = QLineEdit()
        self.website.setPlaceholderText("الموقع الإلكتروني")
        contact_layout.addRow("الموقع الإلكتروني:", self.website)
        
        layout.addWidget(contact_group)
        
        # مجموعة الشخص المسؤول
        contact_person_group = QGroupBox("الشخص المسؤول")
        contact_person_group.setStyleSheet(AppStyles.get_frame_style())
        contact_person_layout = QFormLayout(contact_person_group)
        
        self.contact_person = QLineEdit()
        self.contact_person.setPlaceholderText("اسم الشخص المسؤول")
        contact_person_layout.addRow("الاسم:", self.contact_person)
        
        self.contact_position = QLineEdit()
        self.contact_position.setPlaceholderText("المنصب")
        contact_person_layout.addRow("المنصب:", self.contact_position)
        
        self.contact_phone = QLineEdit()
        self.contact_phone.setPlaceholderText("هاتف الشخص المسؤول")
        contact_person_layout.addRow("الهاتف:", self.contact_phone)
        
        layout.addWidget(contact_person_group)
        layout.addStretch()
    
    def create_financial_tab(self, tab):
        """إنشاء تبويب البيانات المالية"""
        layout = QVBoxLayout(tab)
        
        # مجموعة الشروط المالية
        financial_group = QGroupBox("الشروط المالية")
        financial_group.setStyleSheet(AppStyles.get_frame_style())
        financial_layout = QFormLayout(financial_group)
        
        self.currency = QComboBox()
        self.currency.addItems(["ريال سعودي", "دولار أمريكي", "يورو", "درهم إماراتي"])
        financial_layout.addRow("العملة:", self.currency)
        
        self.payment_terms = QComboBox()
        self.payment_terms.addItems(["نقداً", "آجل 30 يوم", "آجل 60 يوم", "آجل 90 يوم"])
        financial_layout.addRow("شروط الدفع:", self.payment_terms)
        
        self.credit_limit = QLineEdit()
        self.credit_limit.setPlaceholderText("حد الائتمان")
        financial_layout.addRow("حد الائتمان:", self.credit_limit)
        
        self.discount_rate = QLineEdit()
        self.discount_rate.setPlaceholderText("معدل الخصم %")
        financial_layout.addRow("معدل الخصم:", self.discount_rate)
        
        layout.addWidget(financial_group)
        
        # مجموعة البيانات المصرفية
        bank_group = QGroupBox("البيانات المصرفية")
        bank_group.setStyleSheet(AppStyles.get_frame_style())
        bank_layout = QFormLayout(bank_group)
        
        self.bank_name = QLineEdit()
        self.bank_name.setPlaceholderText("اسم البنك")
        bank_layout.addRow("اسم البنك:", self.bank_name)
        
        self.account_number = QLineEdit()
        self.account_number.setPlaceholderText("رقم الحساب")
        bank_layout.addRow("رقم الحساب:", self.account_number)
        
        self.iban = QLineEdit()
        self.iban.setPlaceholderText("رقم الآيبان")
        bank_layout.addRow("الآيبان:", self.iban)
        
        layout.addWidget(bank_group)
        
        # مجموعة الإعدادات
        settings_group = QGroupBox("الإعدادات")
        settings_group.setStyleSheet(AppStyles.get_frame_style())
        settings_layout = QVBoxLayout(settings_group)
        
        self.is_active = QCheckBox("مورد نشط")
        self.is_active.setChecked(True)
        settings_layout.addWidget(self.is_active)
        
        self.is_preferred = QCheckBox("مورد مفضل")
        settings_layout.addWidget(self.is_preferred)
        
        self.send_notifications = QCheckBox("إرسال الإشعارات")
        self.send_notifications.setChecked(True)
        settings_layout.addWidget(self.send_notifications)
        
        layout.addWidget(settings_group)
        layout.addStretch()
    
    def create_table_section(self, layout):
        """إنشاء قسم الجدول"""
        table_frame = QFrame()
        table_layout = QVBoxLayout(table_frame)
        
        # عنوان الجدول
        table_title = QLabel("الموردين المسجلين")
        table_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        table_layout.addWidget(table_title)
        
        # الجدول
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(7)
        self.suppliers_table.setHorizontalHeaderLabels([
            "الرمز", "الاسم", "النوع", "البلد", "الهاتف", "البريد الإلكتروني", "الحالة"
        ])
        
        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.suppliers_table)
        
        # إضافة بيانات تجريبية
        self.suppliers_table.setRowCount(8)
        suppliers_data = [
            ["SUP001", "شركة التقنية المتقدمة", "شركة", "السعودية", "011-4567890", "<EMAIL>", "نشط"],
            ["SUP002", "مؤسسة الأزياء العصرية", "مؤسسة فردية", "الإمارات", "02-3456789", "<EMAIL>", "نشط"],
            ["SUP003", "مصنع الأغذية الطازجة", "مصنع", "السعودية", "013-2345678", "<EMAIL>", "نشط"],
            ["SUP004", "شركة مواد البناء", "شركة", "الكويت", "965-1234567", "<EMAIL>", "نشط"],
            ["SUP005", "مستورد الإلكترونيات", "مستورد", "قطر", "974-9876543", "<EMAIL>", "نشط"],
            ["SUP006", "موزع المنسوجات", "موزع", "البحرين", "973-5432109", "<EMAIL>", "نشط"],
            ["SUP007", "شركة الأدوات المنزلية", "شركة", "عمان", "968-8765432", "<EMAIL>", "غير نشط"],
            ["SUP008", "مؤسسة القرطاسية", "مؤسسة فردية", "السعودية", "012-6789012", "<EMAIL>", "نشط"]
        ]
        
        for row, data in enumerate(suppliers_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 2:  # عمود النوع
                    if value == "شركة":
                        item.setBackground(AppStyles.get_color("primary_light"))
                    elif value == "مصنع":
                        item.setBackground(AppStyles.get_color("info_light"))
                    elif value == "مستورد":
                        item.setBackground(AppStyles.get_color("success_light"))
                    else:
                        item.setBackground(AppStyles.get_color("warning_light"))
                elif col == 6:  # عمود الحالة
                    if value == "نشط":
                        item.setBackground(AppStyles.get_color("success_light"))
                    else:
                        item.setBackground(AppStyles.get_color("secondary_light"))
                
                self.suppliers_table.setItem(row, col, item)
        
        table_layout.addWidget(self.suppliers_table)
        layout.addWidget(table_frame)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر إضافة
        add_btn = QPushButton("إضافة مورد")
        add_btn.setIcon(QIcon("assets/icons/add.png"))
        add_btn.clicked.connect(self.add_supplier)
        add_btn.setStyleSheet(AppStyles.get_button_style("primary"))
        buttons_layout.addWidget(add_btn)
        
        # زر تعديل
        edit_btn = QPushButton("تعديل")
        edit_btn.setIcon(QIcon("assets/icons/edit.png"))
        edit_btn.clicked.connect(self.edit_supplier)
        edit_btn.setStyleSheet(AppStyles.get_button_style("info"))
        buttons_layout.addWidget(edit_btn)
        
        # زر حفظ
        save_btn = QPushButton("حفظ")
        save_btn.setIcon(QIcon("assets/icons/save.png"))
        save_btn.clicked.connect(self.save_supplier)
        save_btn.setStyleSheet(AppStyles.get_button_style("success"))
        buttons_layout.addWidget(save_btn)
        
        # زر حذف
        delete_btn = QPushButton("حذف")
        delete_btn.setIcon(QIcon("assets/icons/delete.png"))
        delete_btn.clicked.connect(self.delete_supplier)
        delete_btn.setStyleSheet(AppStyles.get_button_style("danger"))
        buttons_layout.addWidget(delete_btn)
        
        buttons_layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setIcon(QIcon("assets/icons/close.png"))
        close_btn.clicked.connect(self.close_screen)
        close_btn.setStyleSheet(AppStyles.get_button_style("secondary"))
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)
    
    def add_supplier(self):
        """إضافة مورد جديد"""
        if not self.supplier_code.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز المورد")
            return
        
        if not self.supplier_name_ar.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المورد")
            return
        
        QMessageBox.information(self, "نجح", "تم إضافة المورد بنجاح!")
        self.clear_form()
    
    def edit_supplier(self):
        """تعديل مورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للتعديل")
            return
        
        self.load_supplier_data(current_row)
    
    def save_supplier(self):
        """حفظ المورد"""
        try:
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ بيانات المورد بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
    
    def delete_supplier(self):
        """حذف مورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للحذف")
            return
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   "هل تريد حذف المورد المختار؟")
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الحذف", "تم حذف المورد بنجاح!")
    
    def load_supplier_data(self, row):
        """تحميل بيانات المورد"""
        self.supplier_code.setText(self.suppliers_table.item(row, 0).text())
        self.supplier_name_ar.setText(self.suppliers_table.item(row, 1).text())
        self.phone.setText(self.suppliers_table.item(row, 4).text())
        self.email.setText(self.suppliers_table.item(row, 5).text())
    
    def clear_form(self):
        """مسح النموذج"""
        self.supplier_code.clear()
        self.supplier_name_ar.clear()
        self.supplier_name_en.clear()
        self.address.clear()
        self.phone.clear()
        self.email.clear()
        self.is_active.setChecked(True)
    
    def close_screen(self):
        """إغلاق الشاشة"""
        if hasattr(self, 'main_window'):
            self.window().close()
        else:
            self.parent().close()
