# -*- coding: utf-8 -*-
"""
دعم اللغة العربية وتخطيط RTL
Arabic Language Support and RTL Layout
"""

import arabic_reshaper
from bidi.algorithm import get_display
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QFontDatabase


class ArabicSupport:
    """فئة دعم اللغة العربية"""
    
    def __init__(self):
        self.setup_arabic_fonts()
    
    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية"""
        # قائمة الخطوط المفضلة للعربية
        self.preferred_fonts = [
            "Tahoma",
            "Arial Unicode MS", 
            "Segoe UI",
            "Microsoft Sans Serif",
            "DejaVu Sans"
        ]
        
        # البحث عن أفضل خط متاح
        font_db = QFontDatabase()
        available_fonts = font_db.families()
        
        self.arabic_font = None
        for font_name in self.preferred_fonts:
            if font_name in available_fonts:
                self.arabic_font = QFont(font_name)
                break
        
        if self.arabic_font is None:
            self.arabic_font = QFont()
        
        # إعداد خصائص الخط
        self.arabic_font.setPointSize(10)
        self.arabic_font.setWeight(QFont.Normal)
    
    def get_arabic_font(self, size=10, bold=False):
        """الحصول على خط عربي"""
        font = QFont(self.arabic_font)
        font.setPointSize(size)
        if bold:
            font.setWeight(QFont.Bold)
        return font
    
    def reshape_arabic_text(self, text):
        """إعادة تشكيل النص العربي للعرض الصحيح"""
        if not text:
            return text
        
        try:
            # إعادة تشكيل النص العربي
            reshaped_text = arabic_reshaper.reshape(text)
            # تطبيق خوارزمية BiDi للعرض الصحيح
            display_text = get_display(reshaped_text)
            return display_text
        except Exception:
            # في حالة فشل المعالجة، إرجاع النص الأصلي
            return text
    
    def is_arabic_text(self, text):
        """التحقق من وجود نص عربي"""
        if not text:
            return False
        
        arabic_chars = 0
        total_chars = len(text.replace(' ', ''))
        
        for char in text:
            if '\u0600' <= char <= '\u06FF' or '\u0750' <= char <= '\u077F':
                arabic_chars += 1
        
        # إذا كان أكثر من 30% من النص عربي
        return arabic_chars > 0 and (arabic_chars / max(total_chars, 1)) > 0.3
    
    def get_text_alignment(self, text):
        """تحديد محاذاة النص حسب اللغة"""
        if self.is_arabic_text(text):
            return Qt.AlignRight
        else:
            return Qt.AlignLeft
    
    def setup_widget_rtl(self, widget):
        """إعداد الواجهة للعمل مع RTL"""
        widget.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد الخط العربي
        widget.setFont(self.arabic_font)
        
        return widget
    
    def format_number_arabic(self, number):
        """تنسيق الأرقام للعرض العربي"""
        if number is None:
            return ""
        
        try:
            # تحويل الرقم إلى نص مع فواصل الآلاف
            if isinstance(number, float):
                formatted = f"{number:,.2f}"
            else:
                formatted = f"{int(number):,}"
            
            return formatted
        except:
            return str(number)
    
    def format_currency_arabic(self, amount, currency="ر.س"):
        """تنسيق المبالغ المالية للعرض العربي"""
        if amount is None:
            return ""
        
        try:
            formatted_amount = self.format_number_arabic(amount)
            return f"{formatted_amount} {currency}"
        except:
            return f"{amount} {currency}"
    
    def format_date_arabic(self, date_obj):
        """تنسيق التاريخ للعرض العربي"""
        if date_obj is None:
            return ""
        
        try:
            # تنسيق التاريخ بالشكل العربي
            return date_obj.strftime("%Y/%m/%d")
        except:
            return str(date_obj)
    
    def format_datetime_arabic(self, datetime_obj):
        """تنسيق التاريخ والوقت للعرض العربي"""
        if datetime_obj is None:
            return ""
        
        try:
            # تنسيق التاريخ والوقت بالشكل العربي
            return datetime_obj.strftime("%Y/%m/%d %H:%M")
        except:
            return str(datetime_obj)
    
    def get_month_name_arabic(self, month_number):
        """الحصول على اسم الشهر بالعربية"""
        months = {
            1: "يناير", 2: "فبراير", 3: "مارس", 4: "أبريل",
            5: "مايو", 6: "يونيو", 7: "يوليو", 8: "أغسطس",
            9: "سبتمبر", 10: "أكتوبر", 11: "نوفمبر", 12: "ديسمبر"
        }
        return months.get(month_number, str(month_number))
    
    def get_day_name_arabic(self, day_number):
        """الحصول على اسم اليوم بالعربية"""
        days = {
            0: "الاثنين", 1: "الثلاثاء", 2: "الأربعاء", 3: "الخميس",
            4: "الجمعة", 5: "السبت", 6: "الأحد"
        }
        return days.get(day_number, str(day_number))
    
    def validate_arabic_input(self, text):
        """التحقق من صحة الإدخال العربي"""
        if not text:
            return True
        
        # السماح بالأحرف العربية والإنجليزية والأرقام والرموز الأساسية
        allowed_chars = set()
        
        # الأحرف العربية
        for i in range(0x0600, 0x06FF + 1):
            allowed_chars.add(chr(i))
        
        # الأحرف الإنجليزية
        for i in range(ord('a'), ord('z') + 1):
            allowed_chars.add(chr(i))
            allowed_chars.add(chr(i).upper())
        
        # الأرقام
        for i in range(10):
            allowed_chars.add(str(i))
        
        # الرموز الأساسية
        basic_symbols = " .,;:!?()-_+*/=@#$%^&[]{}|\\\"'`~<>"
        for char in basic_symbols:
            allowed_chars.add(char)
        
        # التحقق من كل حرف
        for char in text:
            if char not in allowed_chars:
                return False
        
        return True
