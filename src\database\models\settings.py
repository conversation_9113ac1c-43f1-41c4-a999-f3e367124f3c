# -*- coding: utf-8 -*-
"""
نماذج الإعدادات العامة
Settings Models for ShipmentPro System
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Float, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from . import Base


class SystemSettings(Base):
    """جدول الإعدادات العامة للنظام"""
    __tablename__ = 'system_settings'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    setting_key = Column(String(100), unique=True, nullable=False, comment="مفتاح الإعداد")
    setting_value = Column(Text, comment="قيمة الإعداد")
    description = Column(String(255), comment="وصف الإعداد")
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="تاريخ التحديث")
    
    def __repr__(self):
        return f"<SystemSettings(key='{self.setting_key}', value='{self.setting_value}')>"


class FiscalYear(Base):
    """جدول السنوات المالية"""
    __tablename__ = 'fiscal_years'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    year_name = Column(String(50), unique=True, nullable=False, comment="اسم السنة المالية")
    start_date = Column(DateTime, nullable=False, comment="تاريخ بداية السنة")
    end_date = Column(DateTime, nullable=False, comment="تاريخ نهاية السنة")
    is_active = Column(Boolean, default=False, comment="السنة النشطة")
    is_closed = Column(Boolean, default=False, comment="السنة مغلقة")
    description = Column(Text, comment="وصف السنة المالية")
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="تاريخ التحديث")
    
    def __repr__(self):
        return f"<FiscalYear(name='{self.year_name}', active={self.is_active})>"


class Currency(Base):
    """جدول العملات"""
    __tablename__ = 'currencies'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(10), unique=True, nullable=False, comment="رمز العملة")
    name = Column(String(100), nullable=False, comment="اسم العملة")
    symbol = Column(String(10), comment="رمز العملة")
    exchange_rate = Column(Float, default=1.0, comment="سعر الصرف")
    is_default = Column(Boolean, default=False, comment="العملة الافتراضية")
    is_active = Column(Boolean, default=True, comment="العملة نشطة")
    description = Column(Text, comment="وصف العملة")
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="تاريخ التحديث")
    
    def __repr__(self):
        return f"<Currency(code='{self.code}', name='{self.name}')>"


class Company(Base):
    """جدول بيانات الشركة"""
    __tablename__ = 'companies'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(200), nullable=False, comment="اسم الشركة")
    name_en = Column(String(200), comment="اسم الشركة بالإنجليزية")
    commercial_register = Column(String(50), comment="السجل التجاري")
    tax_number = Column(String(50), comment="الرقم الضريبي")
    address = Column(Text, comment="العنوان")
    city = Column(String(100), comment="المدينة")
    country = Column(String(100), comment="الدولة")
    postal_code = Column(String(20), comment="الرمز البريدي")
    phone = Column(String(50), comment="الهاتف")
    fax = Column(String(50), comment="الفاكس")
    email = Column(String(100), comment="البريد الإلكتروني")
    website = Column(String(200), comment="الموقع الإلكتروني")
    logo_path = Column(String(500), comment="مسار الشعار")
    is_main_company = Column(Boolean, default=False, comment="الشركة الرئيسية")
    is_active = Column(Boolean, default=True, comment="الشركة نشطة")
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="تاريخ التحديث")
    
    # العلاقات
    branches = relationship("Branch", back_populates="company")
    
    def __repr__(self):
        return f"<Company(name='{self.name}')>"


class Branch(Base):
    """جدول الفروع"""
    __tablename__ = 'branches'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    company_id = Column(Integer, ForeignKey('companies.id'), nullable=False, comment="معرف الشركة")
    name = Column(String(200), nullable=False, comment="اسم الفرع")
    code = Column(String(20), unique=True, comment="رمز الفرع")
    address = Column(Text, comment="العنوان")
    city = Column(String(100), comment="المدينة")
    phone = Column(String(50), comment="الهاتف")
    manager_name = Column(String(100), comment="اسم المدير")
    is_active = Column(Boolean, default=True, comment="الفرع نشط")
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="تاريخ التحديث")
    
    # العلاقات
    company = relationship("Company", back_populates="branches")
    
    def __repr__(self):
        return f"<Branch(name='{self.name}', code='{self.code}')>"


class User(Base):
    """جدول المستخدمين"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False, comment="اسم المستخدم")
    password_hash = Column(String(255), nullable=False, comment="كلمة المرور المشفرة")
    full_name = Column(String(200), nullable=False, comment="الاسم الكامل")
    email = Column(String(100), comment="البريد الإلكتروني")
    phone = Column(String(50), comment="الهاتف")
    is_active = Column(Boolean, default=True, comment="المستخدم نشط")
    is_admin = Column(Boolean, default=False, comment="مدير النظام")
    last_login = Column(DateTime, comment="آخر تسجيل دخول")
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="تاريخ التحديث")
    
    # العلاقات
    permissions = relationship("UserPermission", back_populates="user")
    
    def __repr__(self):
        return f"<User(username='{self.username}', full_name='{self.full_name}')>"


class Permission(Base):
    """جدول الصلاحيات"""
    __tablename__ = 'permissions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), unique=True, nullable=False, comment="اسم الصلاحية")
    description = Column(String(255), comment="وصف الصلاحية")
    module = Column(String(50), comment="الوحدة")
    action = Column(String(50), comment="العملية")
    created_at = Column(DateTime, default=func.now(), comment="تاريخ الإنشاء")
    
    # العلاقات
    user_permissions = relationship("UserPermission", back_populates="permission")
    
    def __repr__(self):
        return f"<Permission(name='{self.name}', module='{self.module}')>"


class UserPermission(Base):
    """جدول صلاحيات المستخدمين"""
    __tablename__ = 'user_permissions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, comment="معرف المستخدم")
    permission_id = Column(Integer, ForeignKey('permissions.id'), nullable=False, comment="معرف الصلاحية")
    granted = Column(Boolean, default=True, comment="الصلاحية ممنوحة")
    granted_at = Column(DateTime, default=func.now(), comment="تاريخ منح الصلاحية")
    granted_by = Column(Integer, ForeignKey('users.id'), comment="من منح الصلاحية")
    
    # العلاقات
    user = relationship("User", back_populates="permissions", foreign_keys=[user_id])
    permission = relationship("Permission", back_populates="user_permissions")
    granter = relationship("User", foreign_keys=[granted_by])
    
    def __repr__(self):
        return f"<UserPermission(user_id={self.user_id}, permission_id={self.permission_id})>"
