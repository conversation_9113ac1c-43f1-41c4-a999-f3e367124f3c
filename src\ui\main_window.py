# -*- coding: utf-8 -*-
"""
النافذة الرئيسية لبرنامج إدارة الشحنات
Main Window for ShipmentPro System
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QMenuBar, QMenu, QStatusBar, QLabel,
                               QToolBar, QPushButton, QFrame, QSplitter,
                               QTreeWidget, QTreeWidgetItem, QStackedWidget,
                               QMessageBox, QApplication)
from PySide6.QtCore import Qt, QSize, Signal
from PySide6.QtGui import QIcon, QFont, QPixmap, QAction

from ..utils.arabic_support import ArabicSupport
from ..utils.styles import AppStyles
from .settings.settings_main import SettingsMainWidget
from .items.items_main import ItemsMainWidget
from .suppliers.suppliers_main import SuppliersMainWidget


class MainWindow(QMainWindow):
    """النافذة الرئيسية للبرنامج"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
        self.setup_connections()
        self.load_settings()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة الرئيسية
        self.setWindowTitle("نظام إدارة الشحنات المتكامل - ShipmentPro")
        self.setMinimumSize(1200, 800)
        
        # تطبيق الأنماط
        self.setStyleSheet(AppStyles.get_main_window_style())
        
        # إنشاء القائمة الرئيسية
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء الواجهة المركزية
        self.create_central_widget()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        menubar.setLayoutDirection(Qt.RightToLeft)
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        # إعدادات قاعدة البيانات
        db_action = QAction("إعدادات قاعدة البيانات", self)
        db_action.setShortcut("Ctrl+D")
        file_menu.addAction(db_action)
        
        file_menu.addSeparator()
        
        # نسخ احتياطي
        backup_action = QAction("إنشاء نسخة احتياطية", self)
        backup_action.setShortcut("Ctrl+B")
        file_menu.addAction(backup_action)
        
        # استعادة نسخة احتياطية
        restore_action = QAction("استعادة نسخة احتياطية", self)
        file_menu.addAction(restore_action)
        
        file_menu.addSeparator()
        
        # خروج
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة الإعدادات
        settings_menu = menubar.addMenu("الإعدادات")
        
        settings_action = QAction("الإعدادات العامة", self)
        settings_action.triggered.connect(lambda: self.show_module("settings"))
        settings_menu.addAction(settings_action)
        
        # قائمة الأصناف
        items_menu = menubar.addMenu("الأصناف")
        
        items_action = QAction("إدارة الأصناف", self)
        items_action.triggered.connect(lambda: self.show_module("items"))
        items_menu.addAction(items_action)
        
        # قائمة الموردين
        suppliers_menu = menubar.addMenu("الموردين")
        
        suppliers_action = QAction("إدارة الموردين", self)
        suppliers_action.triggered.connect(lambda: self.show_module("suppliers"))
        suppliers_menu.addAction(suppliers_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setLayoutDirection(Qt.RightToLeft)
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        
        # زر الإعدادات
        settings_btn = QPushButton("الإعدادات العامة")
        settings_btn.setIcon(QIcon("assets/icons/settings.png"))
        settings_btn.clicked.connect(lambda: self.show_module("settings"))
        toolbar.addWidget(settings_btn)
        
        toolbar.addSeparator()
        
        # زر الأصناف
        items_btn = QPushButton("إدارة الأصناف")
        items_btn.setIcon(QIcon("assets/icons/items.png"))
        items_btn.clicked.connect(lambda: self.show_module("items"))
        toolbar.addWidget(items_btn)
        
        # زر الموردين
        suppliers_btn = QPushButton("إدارة الموردين")
        suppliers_btn.setIcon(QIcon("assets/icons/suppliers.png"))
        suppliers_btn.clicked.connect(lambda: self.show_module("suppliers"))
        toolbar.addWidget(suppliers_btn)
        
        toolbar.addSeparator()
        
        # زر الشحنات (قيد التطوير)
        shipments_btn = QPushButton("متابعة الشحنات")
        shipments_btn.setIcon(QIcon("assets/icons/shipments.png"))
        shipments_btn.setEnabled(False)
        shipments_btn.setToolTip("قيد التطوير")
        toolbar.addWidget(shipments_btn)
    
    def create_central_widget(self):
        """إنشاء الواجهة المركزية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # إنشاء المقسم
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # إنشاء القائمة الجانبية
        self.create_sidebar(splitter)
        
        # إنشاء منطقة المحتوى
        self.create_content_area(splitter)
        
        # تعيين نسب المقسم
        splitter.setSizes([250, 950])
    
    def create_sidebar(self, parent):
        """إنشاء القائمة الجانبية"""
        sidebar_frame = QFrame()
        sidebar_frame.setFrameStyle(QFrame.StyledPanel)
        sidebar_frame.setMaximumWidth(300)
        sidebar_frame.setMinimumWidth(200)
        
        sidebar_layout = QVBoxLayout(sidebar_frame)
        
        # عنوان القائمة
        title_label = QLabel("الأنظمة الرئيسية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(AppStyles.get_sidebar_title_style())
        sidebar_layout.addWidget(title_label)
        
        # شجرة القوائم
        self.menu_tree = QTreeWidget()
        self.menu_tree.setHeaderHidden(True)
        self.menu_tree.setLayoutDirection(Qt.RightToLeft)
        self.menu_tree.setStyleSheet(AppStyles.get_tree_style())
        
        # إضافة عناصر القائمة
        self.populate_menu_tree()
        
        sidebar_layout.addWidget(self.menu_tree)
        parent.addWidget(sidebar_frame)
    
    def populate_menu_tree(self):
        """ملء شجرة القوائم"""
        # نظام الإعدادات العامة
        settings_item = QTreeWidgetItem(["الإعدادات العامة"])
        settings_item.setData(0, Qt.UserRole, "settings")
        
        settings_sub_items = [
            ("المتغيرات العامة", "system_variables"),
            ("السنة المالية", "fiscal_year"),
            ("العملات", "currencies"),
            ("بيانات الشركة", "company_data"),
            ("المستخدمين", "users"),
            ("الصلاحيات", "permissions"),
            ("فتح سنة جديدة", "new_year")
        ]
        
        for text, data in settings_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            settings_item.addChild(sub_item)
        
        self.menu_tree.addTopLevelItem(settings_item)
        
        # نظام إدارة الأصناف
        items_item = QTreeWidgetItem(["إدارة الأصناف"])
        items_item.setData(0, Qt.UserRole, "items")
        
        items_sub_items = [
            ("وحدات القياس", "units"),
            ("مجموعات الأصناف", "item_groups"),
            ("بيانات الأصناف", "items_data")
        ]
        
        for text, data in items_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            items_item.addChild(sub_item)
        
        self.menu_tree.addTopLevelItem(items_item)
        
        # نظام إدارة الموردين
        suppliers_item = QTreeWidgetItem(["إدارة الموردين"])
        suppliers_item.setData(0, Qt.UserRole, "suppliers")
        
        suppliers_sub_items = [
            ("بيانات الموردين", "suppliers_data"),
            ("عمليات الموردين", "supplier_transactions"),
            ("التقارير", "supplier_reports")
        ]
        
        for text, data in suppliers_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            suppliers_item.addChild(sub_item)
        
        self.menu_tree.addTopLevelItem(suppliers_item)
        
        # الأنظمة قيد التطوير
        dev_item = QTreeWidgetItem(["قيد التطوير"])
        dev_item.setDisabled(True)
        
        dev_sub_items = [
            ("متابعة الشحنات", "shipments"),
            ("الإدخالات الجمركية", "customs"),
            ("التكاليف", "costs")
        ]
        
        for text, data in dev_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            sub_item.setDisabled(True)
            dev_item.addChild(sub_item)
        
        self.menu_tree.addTopLevelItem(dev_item)
        
        # توسيع العناصر الرئيسية
        self.menu_tree.expandAll()
    
    def create_content_area(self, parent):
        """إنشاء منطقة المحتوى"""
        # إطار المحتوى
        content_frame = QFrame()
        content_frame.setFrameStyle(QFrame.StyledPanel)
        
        content_layout = QVBoxLayout(content_frame)
        
        # عنوان المحتوى
        self.content_title = QLabel("مرحباً بك في نظام إدارة الشحنات المتكامل")
        self.content_title.setAlignment(Qt.AlignCenter)
        self.content_title.setStyleSheet(AppStyles.get_content_title_style())
        content_layout.addWidget(self.content_title)
        
        # منطقة المحتوى المكدسة
        self.content_stack = QStackedWidget()
        content_layout.addWidget(self.content_stack)
        
        # إضافة الصفحات
        self.add_content_pages()
        
        parent.addWidget(content_frame)
    
    def add_content_pages(self):
        """إضافة صفحات المحتوى"""
        # صفحة الترحيب
        welcome_widget = self.create_welcome_widget()
        self.content_stack.addWidget(welcome_widget)
        
        # صفحة الإعدادات
        self.settings_widget = SettingsMainWidget()
        self.content_stack.addWidget(self.settings_widget)
        
        # صفحة الأصناف
        self.items_widget = ItemsMainWidget()
        self.content_stack.addWidget(self.items_widget)
        
        # صفحة الموردين
        self.suppliers_widget = SuppliersMainWidget()
        self.content_stack.addWidget(self.suppliers_widget)
    
    def create_welcome_widget(self):
        """إنشاء صفحة الترحيب"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        
        # شعار البرنامج
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("QLabel { font-size: 48px; color: #2c3e50; font-weight: bold; }")
        logo_label.setText("🚢 ShipmentPro")
        layout.addWidget(logo_label)
        
        # وصف البرنامج
        desc_label = QLabel("نظام إدارة الشحنات المتكامل والمتقدم")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("QLabel { font-size: 18px; color: #34495e; margin: 20px; }")
        layout.addWidget(desc_label)
        
        # معلومات الإصدار
        version_label = QLabel("الإصدار 1.0.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("QLabel { font-size: 14px; color: #7f8c8d; }")
        layout.addWidget(version_label)
        
        return widget
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()
        status_bar.setLayoutDirection(Qt.RightToLeft)
        
        # معلومات المستخدم
        self.user_label = QLabel("المستخدم: مدير النظام")
        status_bar.addPermanentWidget(self.user_label)
        
        # معلومات قاعدة البيانات
        self.db_label = QLabel("قاعدة البيانات: متصلة")
        status_bar.addPermanentWidget(self.db_label)
        
        # الوقت والتاريخ
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        self.time_label = QLabel(f"التاريخ: {current_time}")
        status_bar.addPermanentWidget(self.time_label)
        
        status_bar.showMessage("مرحباً بك في نظام إدارة الشحنات")
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.menu_tree.itemClicked.connect(self.on_menu_item_clicked)
    
    def on_menu_item_clicked(self, item, column):
        """معالج النقر على عنصر القائمة"""
        data = item.data(0, Qt.UserRole)
        if data:
            self.show_module(data)
    
    def show_module(self, module_name):
        """عرض وحدة معينة"""
        if module_name == "settings" or module_name.startswith("system_") or module_name in ["fiscal_year", "currencies", "company_data", "users", "permissions", "new_year"]:
            self.content_stack.setCurrentWidget(self.settings_widget)
            self.content_title.setText("الإعدادات العامة")
            if hasattr(self.settings_widget, 'show_sub_module'):
                self.settings_widget.show_sub_module(module_name)
        
        elif module_name == "items" or module_name in ["units", "item_groups", "items_data"]:
            self.content_stack.setCurrentWidget(self.items_widget)
            self.content_title.setText("إدارة الأصناف")
            if hasattr(self.items_widget, 'show_sub_module'):
                self.items_widget.show_sub_module(module_name)
        
        elif module_name == "suppliers" or module_name in ["suppliers_data", "supplier_transactions", "supplier_reports"]:
            self.content_stack.setCurrentWidget(self.suppliers_widget)
            self.content_title.setText("إدارة الموردين")
            if hasattr(self.suppliers_widget, 'show_sub_module'):
                self.suppliers_widget.show_sub_module(module_name)
        
        else:
            # عرض رسالة للأنظمة قيد التطوير
            QMessageBox.information(self, "قيد التطوير", f"النظام '{module_name}' قيد التطوير حالياً")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
        <div style='text-align: center; direction: rtl;'>
        <h2>نظام إدارة الشحنات المتكامل</h2>
        <h3>ShipmentPro v1.0.0</h3>
        <p>نظام متكامل ومتقدم لإدارة الشحنات والموردين والأصناف</p>
        <p>يدعم اللغة العربية بالكامل مع تخطيط RTL</p>
        <p>تم التطوير باستخدام Python و PySide6</p>
        <p>© 2025 ShipmentPro Solutions</p>
        </div>
        """
        QMessageBox.about(self, "حول البرنامج", about_text)
    
    def load_settings(self):
        """تحميل الإعدادات"""
        # تحميل إعدادات النافذة والتطبيق
        pass
    
    def closeEvent(self, event):
        """معالج إغلاق النافذة"""
        reply = QMessageBox.question(
            self, 
            "تأكيد الخروج",
            "هل تريد إغلاق البرنامج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
