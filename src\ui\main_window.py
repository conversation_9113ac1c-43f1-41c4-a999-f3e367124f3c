# -*- coding: utf-8 -*-
"""
النافذة الرئيسية لبرنامج إدارة الشحنات
Main Window for ShipmentPro System
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QMenuBar, QMenu, QStatusBar, QLabel,
                               QToolBar, QPushButton, QFrame, QSplitter,
                               QTreeWidget, QTreeWidgetItem, QStackedWidget,
                               QMessageBox, QApplication)
from PySide6.QtCore import Qt, QSize, Signal
from PySide6.QtGui import QIcon, QFont, QPixmap, QAction

from ..utils.arabic_support import ArabicSupport
from ..utils.styles import AppStyles
from .settings.settings_main import SettingsMainWidget
from .items.items_main import ItemsMainWidget
from .suppliers.suppliers_main import SuppliersMainWidget
from .customs.customs_main import CustomsMainWidget
from .costs.costs_main import CostsMainWidget
from .shipments.shipments_main import ShipmentsMainWidget


class MainWindow(QMainWindow):
    """النافذة الرئيسية للبرنامج"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
        self.setup_connections()
        self.load_settings()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة الرئيسية
        self.setWindowTitle("نظام إدارة الشحنات المتكامل - ShipmentPro")
        self.setMinimumSize(1200, 800)
        
        # تطبيق الأنماط
        self.setStyleSheet(AppStyles.get_main_window_style())
        
        # إنشاء القائمة الرئيسية
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء الواجهة المركزية
        self.create_central_widget()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        menubar.setLayoutDirection(Qt.RightToLeft)
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        # إعدادات قاعدة البيانات
        db_action = QAction("إعدادات قاعدة البيانات", self)
        db_action.setShortcut("Ctrl+D")
        file_menu.addAction(db_action)
        
        file_menu.addSeparator()
        
        # نسخ احتياطي
        backup_action = QAction("إنشاء نسخة احتياطية", self)
        backup_action.setShortcut("Ctrl+B")
        file_menu.addAction(backup_action)
        
        # استعادة نسخة احتياطية
        restore_action = QAction("استعادة نسخة احتياطية", self)
        file_menu.addAction(restore_action)
        
        file_menu.addSeparator()
        
        # خروج
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة الإعدادات
        settings_menu = menubar.addMenu("الإعدادات")
        
        settings_action = QAction("الإعدادات العامة", self)
        settings_action.triggered.connect(lambda: self.show_module("settings"))
        settings_menu.addAction(settings_action)
        
        # قائمة الأصناف
        items_menu = menubar.addMenu("الأصناف")
        
        items_action = QAction("إدارة الأصناف", self)
        items_action.triggered.connect(lambda: self.show_module("items"))
        items_menu.addAction(items_action)
        
        # قائمة الموردين
        suppliers_menu = menubar.addMenu("الموردين")
        
        suppliers_action = QAction("إدارة الموردين", self)
        suppliers_action.triggered.connect(lambda: self.show_module("suppliers"))
        suppliers_menu.addAction(suppliers_action)

        # قائمة الشحنات
        shipments_menu = menubar.addMenu("الشحنات")

        shipments_action = QAction("تتبع الشحنات", self)
        shipments_action.triggered.connect(lambda: self.show_module("shipments"))
        shipments_menu.addAction(shipments_action)

        # قائمة الجمارك
        customs_menu = menubar.addMenu("الجمارك")

        customs_action = QAction("إدارة الجمارك", self)
        customs_action.triggered.connect(lambda: self.show_module("customs"))
        customs_menu.addAction(customs_action)

        # قائمة التكاليف
        costs_menu = menubar.addMenu("التكاليف")

        costs_action = QAction("إدارة التكاليف", self)
        costs_action.triggered.connect(lambda: self.show_module("costs"))
        costs_menu.addAction(costs_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setLayoutDirection(Qt.RightToLeft)
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        
        # زر الإعدادات
        settings_btn = QPushButton("الإعدادات العامة")
        settings_btn.setIcon(QIcon("assets/icons/settings.png"))
        settings_btn.clicked.connect(lambda: self.show_module("settings"))
        toolbar.addWidget(settings_btn)
        
        toolbar.addSeparator()
        
        # زر الأصناف
        items_btn = QPushButton("إدارة الأصناف")
        items_btn.setIcon(QIcon("assets/icons/items.png"))
        items_btn.clicked.connect(lambda: self.show_module("items"))
        toolbar.addWidget(items_btn)
        
        # زر الموردين
        suppliers_btn = QPushButton("إدارة الموردين")
        suppliers_btn.setIcon(QIcon("assets/icons/suppliers.png"))
        suppliers_btn.clicked.connect(lambda: self.show_module("suppliers"))
        toolbar.addWidget(suppliers_btn)
        
        toolbar.addSeparator()

        # زر الشحنات
        shipments_btn = QPushButton("تتبع الشحنات")
        shipments_btn.setIcon(QIcon("assets/icons/shipments.png"))
        shipments_btn.clicked.connect(lambda: self.show_module("shipments"))
        toolbar.addWidget(shipments_btn)

        # زر الجمارك
        customs_btn = QPushButton("إدارة الجمارك")
        customs_btn.setIcon(QIcon("assets/icons/customs.png"))
        customs_btn.clicked.connect(lambda: self.show_module("customs"))
        toolbar.addWidget(customs_btn)

        # زر التكاليف
        costs_btn = QPushButton("إدارة التكاليف")
        costs_btn.setIcon(QIcon("assets/icons/costs.png"))
        costs_btn.clicked.connect(lambda: self.show_module("costs"))
        toolbar.addWidget(costs_btn)
    
    def create_central_widget(self):
        """إنشاء الواجهة المركزية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # إنشاء المقسم
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # إنشاء القائمة الجانبية
        self.create_sidebar(splitter)
        
        # إنشاء منطقة المحتوى
        self.create_content_area(splitter)
        
        # تعيين نسب المقسم
        splitter.setSizes([250, 950])
    
    def create_sidebar(self, parent):
        """إنشاء القائمة الجانبية"""
        sidebar_frame = QFrame()
        sidebar_frame.setFrameStyle(QFrame.StyledPanel)
        sidebar_frame.setMaximumWidth(300)
        sidebar_frame.setMinimumWidth(200)
        
        sidebar_layout = QVBoxLayout(sidebar_frame)
        
        # عنوان القائمة
        title_label = QLabel("الأنظمة الرئيسية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(AppStyles.get_sidebar_title_style())
        sidebar_layout.addWidget(title_label)
        
        # شجرة القوائم
        self.menu_tree = QTreeWidget()
        self.menu_tree.setHeaderHidden(True)
        self.menu_tree.setLayoutDirection(Qt.RightToLeft)
        self.menu_tree.setStyleSheet(AppStyles.get_tree_style())
        
        # إضافة عناصر القائمة
        self.populate_menu_tree()
        
        sidebar_layout.addWidget(self.menu_tree)
        parent.addWidget(sidebar_frame)
    
    def populate_menu_tree(self):
        """ملء شجرة القوائم"""
        # نظام الإعدادات العامة
        settings_item = QTreeWidgetItem(["الإعدادات العامة"])
        settings_item.setData(0, Qt.UserRole, "settings")
        
        settings_sub_items = [
            ("المتغيرات العامة", "system_variables"),
            ("السنة المالية", "fiscal_year"),
            ("العملات", "currencies"),
            ("بيانات الشركة", "company_data"),
            ("المستخدمين", "users"),
            ("الصلاحيات", "permissions"),
            ("فتح سنة جديدة", "new_year")
        ]
        
        for text, data in settings_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            settings_item.addChild(sub_item)
        
        self.menu_tree.addTopLevelItem(settings_item)
        
        # نظام إدارة الأصناف
        items_item = QTreeWidgetItem(["إدارة الأصناف"])
        items_item.setData(0, Qt.UserRole, "items")
        
        items_sub_items = [
            ("وحدات القياس", "units"),
            ("مجموعات الأصناف", "item_groups"),
            ("بيانات الأصناف", "items_data")
        ]
        
        for text, data in items_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            items_item.addChild(sub_item)
        
        self.menu_tree.addTopLevelItem(items_item)
        
        # نظام إدارة الموردين
        suppliers_item = QTreeWidgetItem(["إدارة الموردين"])
        suppliers_item.setData(0, Qt.UserRole, "suppliers")
        
        suppliers_sub_items = [
            ("بيانات الموردين", "suppliers_data"),
            ("عمليات الموردين", "supplier_transactions"),
            ("التقارير", "supplier_reports")
        ]
        
        for text, data in suppliers_sub_items:
            sub_item = QTreeWidgetItem([text])
            sub_item.setData(0, Qt.UserRole, data)
            suppliers_item.addChild(sub_item)
        
        self.menu_tree.addTopLevelItem(suppliers_item)
        
        # نظام تتبع الشحنات
        shipments_item = QTreeWidgetItem(["تتبع الشحنات"])
        shipments_item.setData(0, Qt.UserRole, "shipments")
        self.menu_tree.addTopLevelItem(shipments_item)

        # نظام إدارة الجمارك
        customs_item = QTreeWidgetItem(["إدارة الجمارك"])
        customs_item.setData(0, Qt.UserRole, "customs")
        self.menu_tree.addTopLevelItem(customs_item)

        # نظام إدارة التكاليف
        costs_item = QTreeWidgetItem(["إدارة التكاليف"])
        costs_item.setData(0, Qt.UserRole, "costs")
        self.menu_tree.addTopLevelItem(costs_item)
        
        # توسيع العناصر الرئيسية
        self.menu_tree.expandAll()
    
    def create_content_area(self, parent):
        """إنشاء منطقة المحتوى"""
        # إطار المحتوى
        content_frame = QFrame()
        content_frame.setFrameStyle(QFrame.StyledPanel)
        
        content_layout = QVBoxLayout(content_frame)
        
        # عنوان المحتوى
        self.content_title = QLabel("مرحباً بك في نظام إدارة الشحنات المتكامل")
        self.content_title.setAlignment(Qt.AlignCenter)
        self.content_title.setStyleSheet(AppStyles.get_content_title_style())
        content_layout.addWidget(self.content_title)
        
        # منطقة المحتوى المكدسة
        self.content_stack = QStackedWidget()
        content_layout.addWidget(self.content_stack)
        
        # إضافة الصفحات
        self.add_content_pages()
        
        parent.addWidget(content_frame)
    
    def add_content_pages(self):
        """إضافة صفحات المحتوى"""
        # صفحة الترحيب
        welcome_widget = self.create_welcome_widget()
        self.content_stack.addWidget(welcome_widget)
        
        # صفحة الإعدادات
        self.settings_widget = SettingsMainWidget()
        self.content_stack.addWidget(self.settings_widget)
        
        # صفحة الأصناف
        self.items_widget = ItemsMainWidget()
        self.content_stack.addWidget(self.items_widget)
        
        # صفحة الموردين
        self.suppliers_widget = SuppliersMainWidget()
        self.content_stack.addWidget(self.suppliers_widget)
    
    def create_welcome_widget(self):
        """إنشاء صفحة الترحيب"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        
        # شعار البرنامج
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("QLabel { font-size: 48px; color: #2c3e50; font-weight: bold; }")
        logo_label.setText("🚢 ShipmentPro")
        layout.addWidget(logo_label)
        
        # وصف البرنامج
        desc_label = QLabel("نظام إدارة الشحنات المتكامل والمتقدم")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("QLabel { font-size: 18px; color: #34495e; margin: 20px; }")
        layout.addWidget(desc_label)
        
        # معلومات الإصدار
        version_label = QLabel("الإصدار 1.0.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("QLabel { font-size: 14px; color: #7f8c8d; }")
        layout.addWidget(version_label)
        
        return widget
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()
        status_bar.setLayoutDirection(Qt.RightToLeft)
        
        # معلومات المستخدم
        self.user_label = QLabel("المستخدم: مدير النظام")
        status_bar.addPermanentWidget(self.user_label)
        
        # معلومات قاعدة البيانات
        self.db_label = QLabel("قاعدة البيانات: متصلة")
        status_bar.addPermanentWidget(self.db_label)
        
        # الوقت والتاريخ
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        self.time_label = QLabel(f"التاريخ: {current_time}")
        status_bar.addPermanentWidget(self.time_label)
        
        status_bar.showMessage("مرحباً بك في نظام إدارة الشحنات")
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.menu_tree.itemClicked.connect(self.on_menu_item_clicked)
    
    def on_menu_item_clicked(self, item, column):
        """معالج النقر على عنصر القائمة"""
        data = item.data(0, Qt.UserRole)
        if data:
            self.show_module(data)
    
    def show_module(self, module_name):
        """عرض وحدة معينة في نافذة ملء الشاشة"""
        # تحديد العنوان والكلاس المناسب لكل وحدة
        module_configs = {
            # الإعدادات العامة
            "settings": ("الإعدادات العامة", SettingsMainWidget),
            "system_variables": ("المتغيرات العامة", "settings.SystemVariablesWidget"),
            "fiscal_year": ("السنة المالية", "settings.FiscalYearWidget"),
            "currencies": ("العملات", "settings.CurrenciesWidget"),
            "company_data": ("بيانات الشركة", "settings.CompanyDataWidget"),
            "users": ("المستخدمين", "settings.UsersWidget"),
            "permissions": ("الصلاحيات", "settings.PermissionsWidget"),
            "new_year": ("فتح سنة جديدة", "settings.NewYearWidget"),

            # إدارة الأصناف
            "items": ("إدارة الأصناف", ItemsMainWidget),
            "units": ("وحدات القياس", "items.UnitsWidget"),
            "item_groups": ("مجموعات الأصناف", "items.ItemGroupsWidget"),
            "items_data": ("بيانات الأصناف", "items.ItemsDataWidget"),

            # إدارة الموردين
            "suppliers": ("إدارة الموردين", SuppliersMainWidget),
            "suppliers_data": ("بيانات الموردين", "suppliers.SuppliersDataWidget"),
            "supplier_transactions": ("عمليات الموردين", "suppliers.SupplierTransactionsWidget"),
            "supplier_reports": ("تقارير الموردين", "suppliers.SupplierReportsWidget"),

            # الأنظمة الأخرى
            "customs": ("نظام إدارة الجمارك", CustomsMainWidget),
            "costs": ("نظام إدارة التكاليف", CostsMainWidget),
            "shipments": ("نظام تتبع الشحنات", ShipmentsMainWidget),
        }

        if module_name == "welcome":
            # العودة للشاشة الرئيسية
            self.content_stack.setCurrentIndex(0)
            self.content_title.setText("مرحباً بك في نظام إدارة الشحنات المتكامل")
            return

        if module_name in module_configs:
            title, widget_class = module_configs[module_name]

            # إذا كان الكلاس عبارة عن نص، فهو شاشة منفصلة جديدة
            if isinstance(widget_class, str):
                self.open_separate_screen(module_name, title, widget_class)
            else:
                # إذا كان كلاس موجود، استخدم النظام القديم
                self.open_fullscreen_module(module_name, title, widget_class)
        else:
            # عرض رسالة للأنظمة قيد التطوير
            QMessageBox.information(self, "قيد التطوير", f"النظام '{module_name}' قيد التطوير حالياً")

    def open_fullscreen_module(self, module_key, title, widget_class, sub_module=None):
        """فتح نافذة النظام في وضع ملء الشاشة"""
        # إنشاء نافذة جديدة
        module_window = QMainWindow()
        module_window.setWindowTitle(f"{title} - نظام إدارة الشحنات المتكامل")
        module_window.setStyleSheet(AppStyles.get_main_window_style())

        # إضافة شريط أدوات للنافذة
        self.create_module_toolbar(module_window, title)

        # إضافة شريط حالة للنافذة
        self.create_module_statusbar(module_window, title)

        # إضافة اختصارات لوحة المفاتيح
        self.create_module_shortcuts(module_window)

        # إنشاء الواجهة
        widget = widget_class()

        # إضافة مرجع للنافذة الرئيسية للعودة إليها
        widget.main_window = self

        # تعيين الواجهة كمحتوى مركزي
        module_window.setCentralWidget(widget)

        # عرض النافذة في وضع ملء الشاشة
        module_window.showMaximized()

        # إخفاء النافذة الرئيسية
        self.hide()

        # إذا كان هناك وحدة فرعية، عرضها
        if sub_module and hasattr(widget, 'show_sub_module'):
            widget.show_sub_module(sub_module)

        # حفظ مرجع للنافذة لمنع حذفها من الذاكرة
        setattr(self, f"{module_key}_window", module_window)
        setattr(self, f"{module_key}_widget", widget)

        # ربط إغلاق النافذة بالعودة للنافذة الرئيسية
        module_window.closeEvent = lambda event: self.on_module_window_close(event, module_window)

    def create_module_toolbar(self, window, title):
        """إنشاء شريط أدوات للنافذة المستقلة"""
        toolbar = window.addToolBar("أدوات النظام")
        toolbar.setLayoutDirection(Qt.RightToLeft)
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # زر العودة للنافذة الرئيسية
        back_btn = QPushButton("العودة للنافذة الرئيسية")
        back_btn.setIcon(QIcon("assets/icons/back.png"))
        back_btn.clicked.connect(lambda: window.close())
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        toolbar.addWidget(back_btn)

        toolbar.addSeparator()

        # عنوان النظام
        title_label = QLabel(f"🔹 {title}")
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-size: 16px;
                font-weight: bold;
                padding: 5px 10px;
            }
        """)
        toolbar.addWidget(title_label)

        toolbar.addSeparator()

        # زر تصغير النافذة
        minimize_btn = QPushButton("تصغير")
        minimize_btn.setIcon(QIcon("assets/icons/minimize.png"))
        minimize_btn.clicked.connect(lambda: window.showMinimized())
        toolbar.addWidget(minimize_btn)

        # زر استعادة/تكبير النافذة
        restore_btn = QPushButton("استعادة")
        restore_btn.setIcon(QIcon("assets/icons/restore.png"))
        restore_btn.clicked.connect(lambda: window.showNormal() if window.isMaximized() else window.showMaximized())
        toolbar.addWidget(restore_btn)

    def create_module_statusbar(self, window, title):
        """إنشاء شريط حالة للنافذة المستقلة"""
        statusbar = window.statusBar()
        statusbar.setLayoutDirection(Qt.RightToLeft)

        # معلومات النظام
        system_label = QLabel(f"النظام النشط: {title}")
        system_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
        statusbar.addWidget(system_label)

        statusbar.addPermanentWidget(QLabel(" | "))

        # معلومات المستخدم
        user_label = QLabel("المستخدم: مدير النظام")
        user_label.setStyleSheet("color: #27ae60;")
        statusbar.addPermanentWidget(user_label)

        statusbar.addPermanentWidget(QLabel(" | "))

        # الوقت والتاريخ
        from datetime import datetime
        now = datetime.now()
        time_label = QLabel(f"التاريخ: {now.strftime('%Y-%m-%d')} | الوقت: {now.strftime('%H:%M')}")
        time_label.setStyleSheet("color: #7f8c8d;")
        statusbar.addPermanentWidget(time_label)

    def create_module_shortcuts(self, window):
        """إنشاء اختصارات لوحة المفاتيح للنافذة المستقلة"""
        # اختصار العودة للنافذة الرئيسية
        back_shortcut = QAction("العودة", window)
        back_shortcut.setShortcut("Escape")
        back_shortcut.triggered.connect(lambda: window.close())
        window.addAction(back_shortcut)

        # اختصار تصغير النافذة
        minimize_shortcut = QAction("تصغير", window)
        minimize_shortcut.setShortcut("Ctrl+M")
        minimize_shortcut.triggered.connect(lambda: window.showMinimized())
        window.addAction(minimize_shortcut)

        # اختصار تكبير/استعادة النافذة
        maximize_shortcut = QAction("تكبير/استعادة", window)
        maximize_shortcut.setShortcut("F11")
        maximize_shortcut.triggered.connect(lambda: window.showNormal() if window.isMaximized() else window.showMaximized())
        window.addAction(maximize_shortcut)

        # اختصار إغلاق النافذة
        close_shortcut = QAction("إغلاق", window)
        close_shortcut.setShortcut("Alt+F4")
        close_shortcut.triggered.connect(lambda: window.close())
        window.addAction(close_shortcut)

    def open_separate_screen(self, module_key, title, widget_class_path):
        """فتح شاشة منفصلة جديدة"""
        try:
            # إنشاء نافذة جديدة
            screen_window = QMainWindow()
            screen_window.setWindowTitle(f"{title} - نظام إدارة الشحنات المتكامل")
            screen_window.setStyleSheet(AppStyles.get_main_window_style())

            # إضافة شريط أدوات للنافذة
            self.create_module_toolbar(screen_window, title)

            # إضافة شريط حالة للنافذة
            self.create_module_statusbar(screen_window, title)

            # إضافة اختصارات لوحة المفاتيح
            self.create_module_shortcuts(screen_window)

            # إنشاء الواجهة المنفصلة
            widget = self.create_separate_widget(widget_class_path)

            # إضافة مرجع للنافذة الرئيسية للعودة إليها
            widget.main_window = self

            # تعيين الواجهة كمحتوى مركزي
            screen_window.setCentralWidget(widget)

            # عرض النافذة في وضع ملء الشاشة
            screen_window.showMaximized()

            # إخفاء النافذة الرئيسية
            self.hide()

            # حفظ مرجع للنافذة لمنع حذفها من الذاكرة
            setattr(self, f"{module_key}_screen_window", screen_window)
            setattr(self, f"{module_key}_screen_widget", widget)

            # ربط إغلاق النافذة بالعودة للنافذة الرئيسية
            screen_window.closeEvent = lambda event: self.on_module_window_close(event, screen_window)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في فتح الشاشة: {str(e)}")

    def create_separate_widget(self, widget_class_path):
        """إنشاء واجهة منفصلة حسب المسار المحدد"""
        module_path, class_name = widget_class_path.split('.')

        if module_path == "settings":
            return self.create_settings_widget(class_name)
        elif module_path == "items":
            return self.create_items_widget(class_name)
        elif module_path == "suppliers":
            return self.create_suppliers_widget(class_name)
        else:
            raise ValueError(f"مسار الوحدة غير معروف: {widget_class_path}")

    def create_settings_widget(self, class_name):
        """إنشاء واجهات الإعدادات المنفصلة"""
        if class_name == "SystemVariablesWidget":
            from src.ui.settings.system_variables import SystemVariablesWidget
            return SystemVariablesWidget()
        elif class_name == "FiscalYearWidget":
            from src.ui.settings.fiscal_year import FiscalYearWidget
            return FiscalYearWidget()
        elif class_name == "CurrenciesWidget":
            from src.ui.settings.currencies import CurrenciesWidget
            return CurrenciesWidget()
        elif class_name == "CompanyDataWidget":
            from src.ui.settings.company_data import CompanyDataWidget
            return CompanyDataWidget()
        elif class_name == "UsersWidget":
            from src.ui.settings.users import UsersWidget
            return UsersWidget()
        elif class_name == "PermissionsWidget":
            from src.ui.settings.permissions import PermissionsWidget
            return PermissionsWidget()
        elif class_name == "NewYearWidget":
            from src.ui.settings.new_year import NewYearWidget
            return NewYearWidget()
        else:
            raise ValueError(f"كلاس الإعدادات غير معروف: {class_name}")

    def create_items_widget(self, class_name):
        """إنشاء واجهات الأصناف المنفصلة"""
        if class_name == "UnitsWidget":
            from src.ui.items.units import UnitsWidget
            return UnitsWidget()
        elif class_name == "ItemGroupsWidget":
            from src.ui.items.item_groups import ItemGroupsWidget
            return ItemGroupsWidget()
        elif class_name == "ItemsDataWidget":
            from src.ui.items.items_data import ItemsDataWidget
            return ItemsDataWidget()
        else:
            raise ValueError(f"كلاس الأصناف غير معروف: {class_name}")

    def create_suppliers_widget(self, class_name):
        """إنشاء واجهات الموردين المنفصلة"""
        if class_name == "SuppliersDataWidget":
            from src.ui.suppliers.suppliers_data import SuppliersDataWidget
            return SuppliersDataWidget()
        elif class_name == "SupplierTransactionsWidget":
            from src.ui.suppliers.supplier_transactions import SupplierTransactionsWidget
            return SupplierTransactionsWidget()
        elif class_name == "SupplierReportsWidget":
            from src.ui.suppliers.supplier_reports import SupplierReportsWidget
            return SupplierReportsWidget()
        else:
            raise ValueError(f"كلاس الموردين غير معروف: {class_name}")

    def on_module_window_close(self, event, module_window):
        """معالج إغلاق نافذة النظام"""
        # إظهار النافذة الرئيسية
        self.show()
        self.raise_()
        self.activateWindow()

        # قبول حدث الإغلاق
        event.accept()

    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
        <div style='text-align: center; direction: rtl;'>
        <h2>نظام إدارة الشحنات المتكامل</h2>
        <h3>ShipmentPro v1.0.0</h3>
        <p>نظام متكامل ومتقدم لإدارة الشحنات والموردين والأصناف</p>
        <p>يدعم اللغة العربية بالكامل مع تخطيط RTL</p>
        <p>تم التطوير باستخدام Python و PySide6</p>
        <p>© 2025 ShipmentPro Solutions</p>
        </div>
        """
        QMessageBox.about(self, "حول البرنامج", about_text)
    
    def load_settings(self):
        """تحميل الإعدادات"""
        # تحميل إعدادات النافذة والتطبيق
        pass
    
    def closeEvent(self, event):
        """معالج إغلاق النافذة"""
        reply = QMessageBox.question(
            self, 
            "تأكيد الخروج",
            "هل تريد إغلاق البرنامج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
