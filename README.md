# نظام إدارة الشحنات المتكامل - ShipmentPro

## وصف المشروع
نظام شامل ومتقدم لإدارة الشحنات مطور بلغة Python مع دعم كامل للغة العربية وتخطيط RTL.

## المميزات الرئيسية
- **دعم كامل للغة العربية** مع تخطيط RTL
- **واجهة مستخدم احترافية** باستخدام PySide6
- **قاعدة بيانات متقدمة** مع SQLite/MySQL
- **نظام إدارة شامل** للإعدادات والأصناف والموردين
- **تصدير التقارير** بتنسيقات PDF و Excel
- **نسخ احتياطية تلقائية** لقاعدة البيانات

## الأنظمة المتضمنة

### 1. نظام الإعدادات العامة
- المتغيرات العامة للنظام
- إعداد السنة المالية
- تهيئة العملات
- بيانات الشركة والفروع
- إدارة المستخدمين والصلاحيات
- فتح سنة مالية جديدة

### 2. نظام إدارة الأصناف
- إدارة وحدات القياس
- تصنيف مجموعات الأصناف
- بيانات الأصناف التفصيلية
- إدارة التكاليف والأسعار
- تتبع تاريخ الأسعار

### 3. نظام إدارة الموردين
- بيانات الموردين الأساسية
- معلومات الاتصال والعناوين
- إدارة المستندات والملفات
- تتبع المعاملات المالية
- كشوف حسابات الموردين
- تقارير شاملة مع تصدير Excel/PDF

### 4. الأنظمة قيد التطوير
- **نظام تتبع الشحنات**: تتبع الشحنات في الوقت الفعلي
- **نظام إدارة الجمارك**: إدارة الإجراءات والرسوم الجمركية
- **نظام إدارة التكاليف**: حساب وتتبع تكاليف الشحنات

## المتطلبات التقنية
- Python 3.8+
- PySide6
- SQLAlchemy
- arabic-reshaper
- python-bidi
- openpyxl
- reportlab
- Pillow

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق
```bash
python main.py
```

## هيكل المشروع
```
ShipmentPro/
├── main.py                 # نقطة دخول التطبيق
├── requirements.txt        # متطلبات Python
├── config/                 # ملفات الإعدادات
├── src/                    # الكود المصدري
│   ├── database/          # نماذج وإدارة قاعدة البيانات
│   ├── ui/                # واجهات المستخدم
│   └── utils/             # أدوات مساعدة
├── assets/                # الموارد (أيقونات، صور)
├── data/                  # قاعدة البيانات والنسخ الاحتياطية
└── reports/               # التقارير المُصدرة
```

## الاستخدام
1. تشغيل التطبيق في وضع ملء الشاشة
2. استخدام القائمة الجانبية للتنقل بين الأنظمة
3. كل نظام يحتوي على أزرار موحدة: إضافة - تعديل - بحث - حذف - خروج
4. دعم كامل للنصوص العربية مع محاذاة RTL

## الإعدادات
- ملف الإعدادات: `config/config.json`
- قاعدة البيانات: `data/shipmentpro.db`
- النسخ الاحتياطية: `data/backups/`
- التقارير: `reports/`

## المطور
تم تطوير هذا النظام باستخدام أحدث التقنيات والممارسات في البرمجة مع التركيز على:
- الأداء والاستقرار
- سهولة الاستخدام
- الدعم الكامل للغة العربية
- التوافق مع أنظمة Windows

## الترخيص
هذا المشروع مطور للاستخدام التجاري والشخصي.

## الدعم والتطوير
للحصول على الدعم أو طلب تطوير مميزات إضافية، يرجى التواصل مع فريق التطوير.

---
**نظام إدارة الشحنات المتكامل - ShipmentPro**  
*نظام شامل ومتقدم لإدارة جميع عمليات الشحن والتجارة*
