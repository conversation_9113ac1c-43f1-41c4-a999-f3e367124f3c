# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية لنظام إدارة الأصناف
Main Items Management Interface
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                               QPushButton, QLabel, QFrame, QMessageBox, QFormLayout,
                               QLineEdit, QTextEdit, QComboBox, QCheckBox, QSpinBox,
                               QDoubleSpinBox, QGroupBox, QGridLayout, QTableWidget,
                               QTableWidgetItem, QHeaderView, QScrollArea, QTreeWidget,
                               QTreeWidgetItem, QSplitter)
from PySide6.QtCore import Qt

from ...utils.arabic_support import ArabicSupport
from ...utils.styles import AppStyles


class ItemsMainWidget(QWidget):
    """الواجهة الرئيسية لنظام إدارة الأصناف"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setLayoutDirection(Qt.RightToLeft)
        self.tab_widget.setStyleSheet(AppStyles.get_tab_style())
        
        # إضافة التبويبات
        self.add_tabs()
        
        main_layout.addWidget(self.tab_widget)
        
        # إضافة أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def add_tabs(self):
        """إضافة التبويبات"""
        # تبويب وحدات القياس
        units_tab = self.create_units_tab()
        self.tab_widget.addTab(units_tab, "وحدات القياس")
        
        # تبويب مجموعات الأصناف
        groups_tab = self.create_groups_tab()
        self.tab_widget.addTab(groups_tab, "مجموعات الأصناف")
        
        # تبويب بيانات الأصناف
        items_tab = self.create_items_tab()
        self.tab_widget.addTab(items_tab, "بيانات الأصناف")
    
    def create_units_tab(self):
        """إنشاء تبويب وحدات القياس"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # عنوان التبويب
        title = QLabel("إدارة وحدات القياس")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # مجموعة إضافة وحدة قياس جديدة
        add_unit_group = QGroupBox("إضافة وحدة قياس جديدة")
        add_unit_group.setStyleSheet(AppStyles.get_frame_style())
        add_unit_layout = QFormLayout(add_unit_group)

        self.unit_code = QLineEdit()
        self.unit_code.setPlaceholderText("مثال: KG, M, L")
        add_unit_layout.addRow("رمز الوحدة:", self.unit_code)

        self.unit_name = QLineEdit()
        self.unit_name.setPlaceholderText("مثال: كيلوجرام")
        add_unit_layout.addRow("اسم الوحدة:", self.unit_name)

        self.unit_name_en = QLineEdit()
        self.unit_name_en.setPlaceholderText("Example: Kilogram")
        add_unit_layout.addRow("الاسم بالإنجليزية:", self.unit_name_en)

        self.unit_symbol = QLineEdit()
        self.unit_symbol.setPlaceholderText("مثال: كجم")
        add_unit_layout.addRow("رمز الوحدة المختصر:", self.unit_symbol)

        self.unit_type = QComboBox()
        self.unit_type.addItems(["وزن", "طول", "حجم", "مساحة", "عدد", "وقت", "أخرى"])
        add_unit_layout.addRow("نوع الوحدة:", self.unit_type)

        self.base_unit = QComboBox()
        self.base_unit.addItems(["وحدة أساسية", "كيلوجرام", "متر", "لتر"])
        add_unit_layout.addRow("الوحدة الأساسية:", self.base_unit)

        self.conversion_factor = QDoubleSpinBox()
        self.conversion_factor.setRange(0.0001, 999999.9999)
        self.conversion_factor.setValue(1.0000)
        self.conversion_factor.setDecimals(4)
        add_unit_layout.addRow("معامل التحويل:", self.conversion_factor)

        self.unit_active = QCheckBox("وحدة نشطة")
        self.unit_active.setChecked(True)
        add_unit_layout.addRow("حالة الوحدة:", self.unit_active)

        # أزرار إدارة الوحدات
        unit_buttons_layout = QHBoxLayout()

        add_unit_btn = QPushButton("إضافة وحدة")
        add_unit_btn.setStyleSheet(AppStyles.get_success_button_style())
        unit_buttons_layout.addWidget(add_unit_btn)

        update_unit_btn = QPushButton("تحديث وحدة")
        update_unit_btn.setStyleSheet(AppStyles.get_button_style())
        unit_buttons_layout.addWidget(update_unit_btn)

        delete_unit_btn = QPushButton("حذف وحدة")
        delete_unit_btn.setStyleSheet(AppStyles.get_danger_button_style())
        unit_buttons_layout.addWidget(delete_unit_btn)

        unit_buttons_layout.addStretch()
        add_unit_layout.addRow(unit_buttons_layout)

        layout.addWidget(add_unit_group)

        # جدول وحدات القياس
        units_group = QGroupBox("وحدات القياس المسجلة")
        units_group.setStyleSheet(AppStyles.get_frame_style())
        units_layout = QVBoxLayout(units_group)

        self.units_table = QTableWidget()
        self.units_table.setColumnCount(7)
        self.units_table.setHorizontalHeaderLabels([
            "الرمز", "الاسم", "الاسم الإنجليزي", "الرمز المختصر",
            "النوع", "معامل التحويل", "نشطة"
        ])
        self.units_table.horizontalHeader().setStretchLastSection(True)

        # إضافة بيانات تجريبية
        self.units_table.setRowCount(6)
        units_data = [
            ["KG", "كيلوجرام", "Kilogram", "كجم", "وزن", "1.0000", "نعم"],
            ["G", "جرام", "Gram", "جم", "وزن", "0.0010", "نعم"],
            ["M", "متر", "Meter", "م", "طول", "1.0000", "نعم"],
            ["CM", "سنتيمتر", "Centimeter", "سم", "طول", "0.0100", "نعم"],
            ["L", "لتر", "Liter", "لتر", "حجم", "1.0000", "نعم"],
            ["PCS", "قطعة", "Pieces", "قطعة", "عدد", "1.0000", "نعم"]
        ]

        for row, data in enumerate(units_data):
            for col, value in enumerate(data):
                self.units_table.setItem(row, col, QTableWidgetItem(value))

        units_layout.addWidget(self.units_table)
        layout.addWidget(units_group)

        return widget
    
    def create_groups_tab(self):
        """إنشاء تبويب مجموعات الأصناف"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        title = QLabel("إدارة مجموعات الأصناف")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()

        # شجرة المجموعات
        groups_tree_group = QGroupBox("شجرة المجموعات")
        groups_tree_group.setStyleSheet(AppStyles.get_frame_style())
        groups_tree_group.setMaximumWidth(350)
        groups_tree_layout = QVBoxLayout(groups_tree_group)

        self.groups_tree = QTreeWidget()
        self.groups_tree.setHeaderHidden(True)
        self.groups_tree.setLayoutDirection(Qt.RightToLeft)

        # إضافة مجموعات تجريبية
        root_items = [
            "الإلكترونيات",
            "الأجهزة المنزلية",
            "الملابس والأزياء",
            "الأغذية والمشروبات",
            "مواد البناء"
        ]

        for root_name in root_items:
            root_item = QTreeWidgetItem([root_name])
            self.groups_tree.addTopLevelItem(root_item)

            # إضافة مجموعات فرعية
            if root_name == "الإلكترونيات":
                sub_items = ["الهواتف الذكية", "أجهزة الكمبيوتر", "الكاميرات"]
                for sub_name in sub_items:
                    sub_item = QTreeWidgetItem([sub_name])
                    root_item.addChild(sub_item)

        self.groups_tree.expandAll()
        groups_tree_layout.addWidget(self.groups_tree)

        # أزرار إدارة الشجرة
        tree_buttons_layout = QHBoxLayout()

        expand_all_btn = QPushButton("توسيع الكل")
        expand_all_btn.setStyleSheet(AppStyles.get_button_style())
        tree_buttons_layout.addWidget(expand_all_btn)

        collapse_all_btn = QPushButton("طي الكل")
        collapse_all_btn.setStyleSheet(AppStyles.get_button_style())
        tree_buttons_layout.addWidget(collapse_all_btn)

        groups_tree_layout.addLayout(tree_buttons_layout)
        content_layout.addWidget(groups_tree_group)

        # تفاصيل المجموعة
        group_details_group = QGroupBox("تفاصيل المجموعة")
        group_details_group.setStyleSheet(AppStyles.get_frame_style())
        group_details_layout = QVBoxLayout(group_details_group)

        # نموذج إضافة/تعديل المجموعة
        form_layout = QFormLayout()

        self.group_code = QLineEdit()
        self.group_code.setPlaceholderText("رمز المجموعة")
        form_layout.addRow("رمز المجموعة:", self.group_code)

        self.group_name = QLineEdit()
        self.group_name.setPlaceholderText("اسم المجموعة")
        form_layout.addRow("اسم المجموعة:", self.group_name)

        self.group_name_en = QLineEdit()
        self.group_name_en.setPlaceholderText("Group Name in English")
        form_layout.addRow("الاسم بالإنجليزية:", self.group_name_en)

        self.parent_group = QComboBox()
        self.parent_group.addItems(["بدون مجموعة أب", "الإلكترونيات", "الأجهزة المنزلية", "الملابس والأزياء"])
        form_layout.addRow("المجموعة الأب:", self.parent_group)

        self.group_description = QTextEdit()
        self.group_description.setMaximumHeight(80)
        self.group_description.setPlaceholderText("وصف المجموعة")
        form_layout.addRow("الوصف:", self.group_description)

        self.group_active = QCheckBox("مجموعة نشطة")
        self.group_active.setChecked(True)
        form_layout.addRow("حالة المجموعة:", self.group_active)

        group_details_layout.addLayout(form_layout)

        # أزرار إدارة المجموعات
        group_buttons_layout = QHBoxLayout()

        add_group_btn = QPushButton("إضافة مجموعة")
        add_group_btn.setStyleSheet(AppStyles.get_success_button_style())
        group_buttons_layout.addWidget(add_group_btn)

        update_group_btn = QPushButton("تحديث مجموعة")
        update_group_btn.setStyleSheet(AppStyles.get_button_style())
        group_buttons_layout.addWidget(update_group_btn)

        delete_group_btn = QPushButton("حذف مجموعة")
        delete_group_btn.setStyleSheet(AppStyles.get_danger_button_style())
        group_buttons_layout.addWidget(delete_group_btn)

        clear_form_btn = QPushButton("مسح النموذج")
        clear_form_btn.setStyleSheet(AppStyles.get_warning_button_style())
        group_buttons_layout.addWidget(clear_form_btn)

        group_buttons_layout.addStretch()
        group_details_layout.addLayout(group_buttons_layout)

        # إحصائيات المجموعة
        stats_group = QGroupBox("إحصائيات المجموعة")
        stats_layout = QFormLayout(stats_group)

        total_items_label = QLabel("0")
        total_items_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        stats_layout.addRow("عدد الأصناف:", total_items_label)

        active_items_label = QLabel("0")
        active_items_label.setStyleSheet("font-weight: bold; color: #3498db;")
        stats_layout.addRow("الأصناف النشطة:", active_items_label)

        group_details_layout.addWidget(stats_group)

        content_layout.addWidget(group_details_group)
        layout.addLayout(content_layout)

        return widget
    
    def create_items_tab(self):
        """إنشاء تبويب بيانات الأصناف"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        title = QLabel("إدارة بيانات الأصناف")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # تبويبات فرعية لبيانات الأصناف
        items_sub_tabs = QTabWidget()
        items_sub_tabs.setLayoutDirection(Qt.RightToLeft)
        items_sub_tabs.setStyleSheet(AppStyles.get_tab_style())

        # تبويب البيانات الرئيسية
        main_data_tab = self.create_main_data_tab()
        items_sub_tabs.addTab(main_data_tab, "البيانات الرئيسية")

        # تبويب بيانات التكاليف
        costs_tab = self.create_costs_tab()
        items_sub_tabs.addTab(costs_tab, "بيانات التكاليف")

        # تبويب الخيارات المتقدمة
        advanced_tab = self.create_advanced_tab()
        items_sub_tabs.addTab(advanced_tab, "خيارات متقدمة")

        layout.addWidget(items_sub_tabs)

        return widget

    def create_main_data_tab(self):
        """إنشاء تبويب البيانات الرئيسية للأصناف"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # مجموعة البيانات الأساسية
        basic_info_group = QGroupBox("البيانات الأساسية")
        basic_info_group.setStyleSheet(AppStyles.get_frame_style())
        basic_layout = QFormLayout(basic_info_group)

        self.item_code = QLineEdit()
        self.item_code.setPlaceholderText("رمز الصنف")
        basic_layout.addRow("رمز الصنف:", self.item_code)

        self.item_name = QLineEdit()
        self.item_name.setPlaceholderText("اسم الصنف")
        basic_layout.addRow("اسم الصنف:", self.item_name)

        self.item_name_en = QLineEdit()
        self.item_name_en.setPlaceholderText("Item Name in English")
        basic_layout.addRow("الاسم بالإنجليزية:", self.item_name_en)

        self.item_barcode = QLineEdit()
        self.item_barcode.setPlaceholderText("الباركود")
        basic_layout.addRow("الباركود:", self.item_barcode)

        self.item_group_combo = QComboBox()
        self.item_group_combo.addItems(["اختر المجموعة", "الإلكترونيات", "الأجهزة المنزلية", "الملابس"])
        basic_layout.addRow("مجموعة الصنف:", self.item_group_combo)

        self.unit_of_measure_combo = QComboBox()
        self.unit_of_measure_combo.addItems(["اختر الوحدة", "قطعة", "كيلوجرام", "متر", "لتر"])
        basic_layout.addRow("وحدة القياس:", self.unit_of_measure_combo)

        scroll_layout.addWidget(basic_info_group)

        # مجموعة الوصف والمواصفات
        description_group = QGroupBox("الوصف والمواصفات")
        description_group.setStyleSheet(AppStyles.get_frame_style())
        description_layout = QFormLayout(description_group)

        self.item_description = QTextEdit()
        self.item_description.setMaximumHeight(80)
        self.item_description.setPlaceholderText("وصف الصنف")
        description_layout.addRow("الوصف:", self.item_description)

        self.item_specifications = QTextEdit()
        self.item_specifications.setMaximumHeight(80)
        self.item_specifications.setPlaceholderText("المواصفات التقنية")
        description_layout.addRow("المواصفات:", self.item_specifications)

        self.origin_country = QLineEdit()
        self.origin_country.setPlaceholderText("بلد المنشأ")
        description_layout.addRow("بلد المنشأ:", self.origin_country)

        self.brand = QLineEdit()
        self.brand.setPlaceholderText("العلامة التجارية")
        description_layout.addRow("العلامة التجارية:", self.brand)

        self.model = QLineEdit()
        self.model.setPlaceholderText("الموديل")
        description_layout.addRow("الموديل:", self.model)

        scroll_layout.addWidget(description_group)

        # مجموعة الصورة والملاحظات
        media_group = QGroupBox("الصورة والملاحظات")
        media_group.setStyleSheet(AppStyles.get_frame_style())
        media_layout = QFormLayout(media_group)

        image_layout = QHBoxLayout()
        self.image_path = QLineEdit()
        self.image_path.setPlaceholderText("مسار صورة الصنف")
        image_layout.addWidget(self.image_path)

        browse_image_btn = QPushButton("استعراض")
        browse_image_btn.setStyleSheet(AppStyles.get_button_style())
        image_layout.addWidget(browse_image_btn)

        media_layout.addRow("صورة الصنف:", image_layout)

        self.item_notes = QTextEdit()
        self.item_notes.setMaximumHeight(60)
        self.item_notes.setPlaceholderText("ملاحظات إضافية")
        media_layout.addRow("الملاحظات:", self.item_notes)

        scroll_layout.addWidget(media_group)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        return widget

    def create_costs_tab(self):
        """إنشاء تبويب بيانات التكاليف"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة أسعار الشراء
        purchase_group = QGroupBox("أسعار الشراء")
        purchase_group.setStyleSheet(AppStyles.get_frame_style())
        purchase_layout = QFormLayout(purchase_group)

        self.purchase_price = QDoubleSpinBox()
        self.purchase_price.setRange(0.00, 999999.99)
        self.purchase_price.setDecimals(2)
        self.purchase_price.setSuffix(" ريال")
        purchase_layout.addRow("سعر الشراء:", self.purchase_price)

        self.last_purchase_price = QDoubleSpinBox()
        self.last_purchase_price.setRange(0.00, 999999.99)
        self.last_purchase_price.setDecimals(2)
        self.last_purchase_price.setSuffix(" ريال")
        purchase_layout.addRow("آخر سعر شراء:", self.last_purchase_price)

        self.average_cost = QDoubleSpinBox()
        self.average_cost.setRange(0.00, 999999.99)
        self.average_cost.setDecimals(2)
        self.average_cost.setSuffix(" ريال")
        purchase_layout.addRow("متوسط التكلفة:", self.average_cost)

        layout.addWidget(purchase_group)

        # مجموعة أسعار البيع
        selling_group = QGroupBox("أسعار البيع")
        selling_group.setStyleSheet(AppStyles.get_frame_style())
        selling_layout = QFormLayout(selling_group)

        self.selling_price = QDoubleSpinBox()
        self.selling_price.setRange(0.00, 999999.99)
        self.selling_price.setDecimals(2)
        self.selling_price.setSuffix(" ريال")
        selling_layout.addRow("سعر البيع:", self.selling_price)

        self.min_selling_price = QDoubleSpinBox()
        self.min_selling_price.setRange(0.00, 999999.99)
        self.min_selling_price.setDecimals(2)
        self.min_selling_price.setSuffix(" ريال")
        selling_layout.addRow("أقل سعر بيع:", self.min_selling_price)

        self.wholesale_price = QDoubleSpinBox()
        self.wholesale_price.setRange(0.00, 999999.99)
        self.wholesale_price.setDecimals(2)
        self.wholesale_price.setSuffix(" ريال")
        selling_layout.addRow("سعر الجملة:", self.wholesale_price)

        layout.addWidget(selling_group)

        # مجموعة المخزون
        inventory_group = QGroupBox("بيانات المخزون")
        inventory_group.setStyleSheet(AppStyles.get_frame_style())
        inventory_layout = QFormLayout(inventory_group)

        self.current_stock = QDoubleSpinBox()
        self.current_stock.setRange(0.00, 999999.99)
        self.current_stock.setDecimals(2)
        inventory_layout.addRow("الكمية الحالية:", self.current_stock)

        self.min_stock_level = QDoubleSpinBox()
        self.min_stock_level.setRange(0.00, 999999.99)
        self.min_stock_level.setDecimals(2)
        inventory_layout.addRow("الحد الأدنى للمخزون:", self.min_stock_level)

        self.max_stock_level = QDoubleSpinBox()
        self.max_stock_level.setRange(0.00, 999999.99)
        self.max_stock_level.setDecimals(2)
        inventory_layout.addRow("الحد الأقصى للمخزون:", self.max_stock_level)

        self.reorder_point = QDoubleSpinBox()
        self.reorder_point.setRange(0.00, 999999.99)
        self.reorder_point.setDecimals(2)
        inventory_layout.addRow("نقطة إعادة الطلب:", self.reorder_point)

        layout.addWidget(inventory_group)

        # مجموعة الضرائب
        tax_group = QGroupBox("بيانات الضرائب")
        tax_group.setStyleSheet(AppStyles.get_frame_style())
        tax_layout = QFormLayout(tax_group)

        self.tax_rate = QDoubleSpinBox()
        self.tax_rate.setRange(0.00, 100.00)
        self.tax_rate.setDecimals(2)
        self.tax_rate.setSuffix(" %")
        tax_layout.addRow("معدل الضريبة:", self.tax_rate)

        self.tax_exempt = QCheckBox("معفى من الضريبة")
        tax_layout.addRow("الإعفاء الضريبي:", self.tax_exempt)

        layout.addWidget(tax_group)

        return widget

    def create_advanced_tab(self):
        """إنشاء تبويب الخيارات المتقدمة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة حالة الصنف
        status_group = QGroupBox("حالة الصنف")
        status_group.setStyleSheet(AppStyles.get_frame_style())
        status_layout = QFormLayout(status_group)

        self.item_active = QCheckBox("صنف نشط")
        self.item_active.setChecked(True)
        status_layout.addRow("حالة الصنف:", self.item_active)

        self.is_sellable = QCheckBox("قابل للبيع")
        self.is_sellable.setChecked(True)
        status_layout.addRow("البيع:", self.is_sellable)

        self.is_purchasable = QCheckBox("قابل للشراء")
        self.is_purchasable.setChecked(True)
        status_layout.addRow("الشراء:", self.is_purchasable)

        layout.addWidget(status_group)

        # مجموعة التتبع
        tracking_group = QGroupBox("خيارات التتبع")
        tracking_group.setStyleSheet(AppStyles.get_frame_style())
        tracking_layout = QFormLayout(tracking_group)

        self.track_serial_numbers = QCheckBox("تتبع الأرقام التسلسلية")
        tracking_layout.addRow("الأرقام التسلسلية:", self.track_serial_numbers)

        self.track_expiry_date = QCheckBox("تتبع تاريخ الانتهاء")
        tracking_layout.addRow("تاريخ الانتهاء:", self.track_expiry_date)

        self.track_batch_numbers = QCheckBox("تتبع أرقام الدفعات")
        tracking_layout.addRow("أرقام الدفعات:", self.track_batch_numbers)

        layout.addWidget(tracking_group)

        # مجموعة الأبعاد والوزن
        dimensions_group = QGroupBox("الأبعاد والوزن")
        dimensions_group.setStyleSheet(AppStyles.get_frame_style())
        dimensions_layout = QFormLayout(dimensions_group)

        self.item_weight = QDoubleSpinBox()
        self.item_weight.setRange(0.00, 999999.99)
        self.item_weight.setDecimals(3)
        self.item_weight.setSuffix(" كجم")
        dimensions_layout.addRow("الوزن:", self.item_weight)

        self.item_length = QDoubleSpinBox()
        self.item_length.setRange(0.00, 999999.99)
        self.item_length.setDecimals(2)
        self.item_length.setSuffix(" سم")
        dimensions_layout.addRow("الطول:", self.item_length)

        self.item_width = QDoubleSpinBox()
        self.item_width.setRange(0.00, 999999.99)
        self.item_width.setDecimals(2)
        self.item_width.setSuffix(" سم")
        dimensions_layout.addRow("العرض:", self.item_width)

        self.item_height = QDoubleSpinBox()
        self.item_height.setRange(0.00, 999999.99)
        self.item_height.setDecimals(2)
        self.item_height.setSuffix(" سم")
        dimensions_layout.addRow("الارتفاع:", self.item_height)

        layout.addWidget(dimensions_group)

        # مجموعة الموردين
        suppliers_group = QGroupBox("الموردين المفضلين")
        suppliers_group.setStyleSheet(AppStyles.get_frame_style())
        suppliers_layout = QVBoxLayout(suppliers_group)

        self.preferred_suppliers_table = QTableWidget()
        self.preferred_suppliers_table.setColumnCount(4)
        self.preferred_suppliers_table.setHorizontalHeaderLabels([
            "اسم المورد", "سعر الشراء", "مدة التوريد", "ملاحظات"
        ])
        self.preferred_suppliers_table.setMaximumHeight(150)

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.preferred_suppliers_table)

        # إضافة بيانات تجريبية
        self.preferred_suppliers_table.setRowCount(2)
        suppliers_data = [
            ["شركة التقنية المتقدمة", "150.00", "7 أيام", "مورد موثوق"],
            ["مؤسسة الجودة التجارية", "145.00", "10 أيام", "أسعار منافسة"]
        ]

        for row, data in enumerate(suppliers_data):
            for col, value in enumerate(data):
                self.preferred_suppliers_table.setItem(row, col, QTableWidgetItem(value))

        suppliers_layout.addWidget(self.preferred_suppliers_table)

        # أزرار إدارة الموردين
        suppliers_buttons_layout = QHBoxLayout()

        add_supplier_btn = QPushButton("إضافة مورد")
        add_supplier_btn.setStyleSheet(AppStyles.get_success_button_style())
        suppliers_buttons_layout.addWidget(add_supplier_btn)

        remove_supplier_btn = QPushButton("إزالة مورد")
        remove_supplier_btn.setStyleSheet(AppStyles.get_danger_button_style())
        suppliers_buttons_layout.addWidget(remove_supplier_btn)

        suppliers_buttons_layout.addStretch()
        suppliers_layout.addLayout(suppliers_buttons_layout)

        layout.addWidget(suppliers_group)

        return widget
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.StyledPanel)
        buttons_frame.setStyleSheet(AppStyles.get_frame_style())
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        
        # الأزرار الأساسية
        self.add_btn = QPushButton("إضافة")
        self.add_btn.setStyleSheet(AppStyles.get_success_button_style())
        self.add_btn.clicked.connect(self.add_record)

        self.edit_btn = QPushButton("تعديل")
        self.edit_btn.setStyleSheet(AppStyles.get_button_style())
        self.edit_btn.clicked.connect(self.edit_record)

        self.save_btn = QPushButton("حفظ")
        self.save_btn.setStyleSheet(AppStyles.get_success_button_style())
        self.save_btn.clicked.connect(self.save_record)

        self.search_btn = QPushButton("بحث")
        self.search_btn.setStyleSheet(AppStyles.get_button_style())
        self.search_btn.clicked.connect(self.search_records)

        self.delete_btn = QPushButton("حذف")
        self.delete_btn.setStyleSheet(AppStyles.get_danger_button_style())
        self.delete_btn.clicked.connect(self.delete_record)

        self.exit_btn = QPushButton("خروج")
        self.exit_btn.setStyleSheet(AppStyles.get_primary_button_style())
        self.exit_btn.clicked.connect(self.close_items)

        # إضافة الأزرار للتخطيط
        buttons_layout.addWidget(self.add_btn)
        buttons_layout.addWidget(self.edit_btn)
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.search_btn)
        buttons_layout.addWidget(self.delete_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.exit_btn)
        
        layout.addWidget(buttons_frame)

        # إضافة جدول الأصناف
        self.add_items_table(layout)

    def add_items_table(self, layout):
        """إضافة جدول الأصناف"""
        items_table_group = QGroupBox("قائمة الأصناف")
        items_table_group.setStyleSheet(AppStyles.get_frame_style())
        items_table_layout = QVBoxLayout(items_table_group)

        # شريط البحث
        search_layout = QHBoxLayout()

        search_label = QLabel("البحث:")
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالرمز أو الاسم أو الباركود...")
        search_layout.addWidget(self.search_input)

        search_filter_btn = QPushButton("تصفية")
        search_filter_btn.setStyleSheet(AppStyles.get_button_style())
        search_layout.addWidget(search_filter_btn)

        clear_search_btn = QPushButton("مسح")
        clear_search_btn.setStyleSheet(AppStyles.get_warning_button_style())
        search_layout.addWidget(clear_search_btn)

        items_table_layout.addLayout(search_layout)

        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(8)
        self.items_table.setHorizontalHeaderLabels([
            "الرمز", "اسم الصنف", "المجموعة", "الوحدة",
            "سعر الشراء", "سعر البيع", "المخزون", "الحالة"
        ])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.items_table)

        # إضافة بيانات تجريبية
        self.items_table.setRowCount(5)
        items_data = [
            ["ITM001", "لابتوب ديل XPS 13", "الإلكترونيات", "قطعة", "3500.00", "4200.00", "15", "نشط"],
            ["ITM002", "ثلاجة سامسونج 400 لتر", "الأجهزة المنزلية", "قطعة", "1800.00", "2100.00", "8", "نشط"],
            ["ITM003", "قميص قطني أزرق", "الملابس", "قطعة", "45.00", "75.00", "50", "نشط"],
            ["ITM004", "أرز بسمتي", "الأغذية", "كيلوجرام", "8.50", "12.00", "200", "نشط"],
            ["ITM005", "أسمنت بورتلاندي", "مواد البناء", "كيس", "18.00", "25.00", "100", "نشط"]
        ]

        for row, data in enumerate(items_data):
            for col, value in enumerate(data):
                self.items_table.setItem(row, col, QTableWidgetItem(value))

        items_table_layout.addWidget(self.items_table)

        # إحصائيات الأصناف
        stats_layout = QHBoxLayout()

        total_items_label = QLabel("إجمالي الأصناف: 5")
        total_items_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        stats_layout.addWidget(total_items_label)

        active_items_label = QLabel("الأصناف النشطة: 5")
        active_items_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        stats_layout.addWidget(active_items_label)

        low_stock_label = QLabel("أصناف منخفضة المخزون: 2")
        low_stock_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        stats_layout.addWidget(low_stock_label)

        stats_layout.addStretch()
        items_table_layout.addLayout(stats_layout)

        layout.addWidget(items_table_group)
    
    def show_sub_module(self, module_name):
        """عرض وحدة فرعية معينة"""
        tab_mapping = {
            "units": 0,
            "item_groups": 1,
            "items_data": 2
        }
        
        if module_name in tab_mapping:
            self.tab_widget.setCurrentIndex(tab_mapping[module_name])
    
    def add_record(self):
        """إضافة سجل جديد"""
        current_tab = self.tab_widget.currentIndex()
        tab_name = self.tab_widget.tabText(current_tab)
        QMessageBox.information(self, "إضافة", f"إضافة سجل جديد في {tab_name}")
    
    def edit_record(self):
        """تعديل سجل"""
        current_tab = self.tab_widget.currentIndex()
        tab_name = self.tab_widget.tabText(current_tab)
        QMessageBox.information(self, "تعديل", f"تعديل سجل في {tab_name}")
    
    def save_record(self):
        """حفظ السجل"""
        current_tab = self.tab_widget.currentIndex()
        tab_name = self.tab_widget.tabText(current_tab)
        QMessageBox.information(self, "حفظ", f"تم حفظ البيانات في {tab_name} بنجاح")

    def search_records(self):
        """البحث في السجلات"""
        current_tab = self.tab_widget.currentIndex()
        tab_name = self.tab_widget.tabText(current_tab)
        QMessageBox.information(self, "بحث", f"البحث في {tab_name}")

    def delete_record(self):
        """حذف سجل"""
        current_tab = self.tab_widget.currentIndex()
        tab_name = self.tab_widget.tabText(current_tab)
        
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل تريد حذف السجل المحدد من {tab_name}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "حذف", f"تم حذف السجل من {tab_name}")
    
    def close_items(self):
        """إغلاق نافذة الأصناف"""
        self.parent().show_module("welcome")
