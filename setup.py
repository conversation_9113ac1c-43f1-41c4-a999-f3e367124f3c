# -*- coding: utf-8 -*-
"""
ملف إعداد نظام إدارة الشحنات المتكامل
ShipmentPro Setup Script
"""

from setuptools import setup, find_packages
import os

# قراءة ملف README
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# قراءة المتطلبات
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="shipmentpro",
    version="1.0.0",
    author="ShipmentPro Development Team",
    author_email="<EMAIL>",
    description="نظام إدارة الشحنات المتكامل والمتقدم",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/shipmentpro/shipmentpro",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: Microsoft :: Windows",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Office/Business",
        "Topic :: Database",
        "Natural Language :: Arabic",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "shipmentpro=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.json"],
        "assets": ["icons/*", "images/*"],
        "config": ["*.json"],
    },
    zip_safe=False,
    keywords="shipment management arabic rtl database pyside6",
    project_urls={
        "Bug Reports": "https://github.com/shipmentpro/shipmentpro/issues",
        "Source": "https://github.com/shipmentpro/shipmentpro",
        "Documentation": "https://docs.shipmentpro.com",
    },
)
