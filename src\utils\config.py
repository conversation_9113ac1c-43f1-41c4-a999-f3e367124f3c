# -*- coding: utf-8 -*-
"""
إدارة إعدادات التطبيق
Application Configuration Manager
"""

import os
import json
from pathlib import Path
from typing import Any, Dict, Optional


class Config:
    """مدير إعدادات التطبيق"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        تهيئة مدير الإعدادات
        
        Args:
            config_file (str): اسم ملف الإعدادات
        """
        self.config_dir = Path("config")
        self.config_dir.mkdir(exist_ok=True)
        
        self.config_file = self.config_dir / config_file
        self.settings = {}
        
        # الإعدادات الافتراضية
        self.default_settings = {
            "database": {
                "type": "sqlite",
                "path": "data/shipmentpro.db",
                "backup_enabled": True,
                "backup_interval": 24,  # ساعات
                "max_backups": 30
            },
            "ui": {
                "language": "ar",
                "theme": "default",
                "font_size": 10,
                "window_state": "maximized",
                "rtl_enabled": True
            },
            "company": {
                "name": "شركة الشحن المتقدمة",
                "name_en": "Advanced Shipping Company",
                "address": "",
                "phone": "",
                "email": "",
                "website": "",
                "logo_path": ""
            },
            "system": {
                "auto_save": True,
                "auto_save_interval": 5,  # دقائق
                "session_timeout": 480,  # دقائق (8 ساعات)
                "max_login_attempts": 3,
                "password_min_length": 6
            },
            "reports": {
                "default_format": "pdf",
                "output_directory": "reports",
                "include_logo": True,
                "page_size": "A4",
                "orientation": "portrait"
            },
            "security": {
                "encryption_enabled": False,
                "audit_log_enabled": True,
                "password_expiry_days": 90,
                "force_password_change": False
            }
        }
        
        self.load_settings()
    
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                
                # دمج الإعدادات المحملة مع الافتراضية
                self.settings = self._merge_settings(self.default_settings, loaded_settings)
            else:
                # استخدام الإعدادات الافتراضية
                self.settings = self.default_settings.copy()
                self.save_settings()
                
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {str(e)}")
            self.settings = self.default_settings.copy()
    
    def save_settings(self):
        """حفظ الإعدادات في الملف"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {str(e)}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        الحصول على قيمة إعداد
        
        Args:
            key (str): مفتاح الإعداد (يمكن استخدام النقاط للوصول للمستويات العميقة)
            default (Any): القيمة الافتراضية
            
        Returns:
            Any: قيمة الإعداد
        """
        try:
            keys = key.split('.')
            value = self.settings
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
        except:
            return default
    
    def set(self, key: str, value: Any):
        """
        تعيين قيمة إعداد
        
        Args:
            key (str): مفتاح الإعداد
            value (Any): القيمة الجديدة
        """
        try:
            keys = key.split('.')
            current = self.settings
            
            # الوصول إلى المستوى الأخير
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            
            # تعيين القيمة
            current[keys[-1]] = value
            
            # حفظ الإعدادات
            self.save_settings()
            
        except Exception as e:
            print(f"خطأ في تعيين الإعداد {key}: {str(e)}")
    
    def get_database_config(self) -> Dict[str, Any]:
        """الحصول على إعدادات قاعدة البيانات"""
        return self.get('database', {})
    
    def get_ui_config(self) -> Dict[str, Any]:
        """الحصول على إعدادات واجهة المستخدم"""
        return self.get('ui', {})
    
    def get_company_config(self) -> Dict[str, Any]:
        """الحصول على إعدادات الشركة"""
        return self.get('company', {})
    
    def get_system_config(self) -> Dict[str, Any]:
        """الحصول على إعدادات النظام"""
        return self.get('system', {})
    
    def get_reports_config(self) -> Dict[str, Any]:
        """الحصول على إعدادات التقارير"""
        return self.get('reports', {})
    
    def get_security_config(self) -> Dict[str, Any]:
        """الحصول على إعدادات الأمان"""
        return self.get('security', {})
    
    def update_database_config(self, config: Dict[str, Any]):
        """تحديث إعدادات قاعدة البيانات"""
        current_config = self.get_database_config()
        current_config.update(config)
        self.set('database', current_config)
    
    def update_ui_config(self, config: Dict[str, Any]):
        """تحديث إعدادات واجهة المستخدم"""
        current_config = self.get_ui_config()
        current_config.update(config)
        self.set('ui', current_config)
    
    def update_company_config(self, config: Dict[str, Any]):
        """تحديث إعدادات الشركة"""
        current_config = self.get_company_config()
        current_config.update(config)
        self.set('company', current_config)
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        self.settings = self.default_settings.copy()
        self.save_settings()
    
    def backup_settings(self, backup_path: Optional[str] = None):
        """إنشاء نسخة احتياطية من الإعدادات"""
        try:
            if backup_path is None:
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = self.config_dir / f"config_backup_{timestamp}.json"
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=4)
            
            return str(backup_path)
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء نسخة احتياطية من الإعدادات: {str(e)}")
    
    def restore_settings(self, backup_path: str):
        """استعادة الإعدادات من نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                raise Exception("ملف النسخة الاحتياطية غير موجود")
            
            with open(backup_path, 'r', encoding='utf-8') as f:
                backup_settings = json.load(f)
            
            # التحقق من صحة الإعدادات
            if self._validate_settings(backup_settings):
                self.settings = backup_settings
                self.save_settings()
            else:
                raise Exception("ملف الإعدادات غير صحيح")
                
        except Exception as e:
            raise Exception(f"خطأ في استعادة الإعدادات: {str(e)}")
    
    def _merge_settings(self, default: Dict, loaded: Dict) -> Dict:
        """دمج الإعدادات المحملة مع الافتراضية"""
        result = default.copy()
        
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_settings(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _validate_settings(self, settings: Dict) -> bool:
        """التحقق من صحة الإعدادات"""
        try:
            # التحقق من وجود الأقسام الأساسية
            required_sections = ['database', 'ui', 'company', 'system']
            
            for section in required_sections:
                if section not in settings:
                    return False
            
            return True
            
        except:
            return False
    
    def export_settings(self, export_path: str):
        """تصدير الإعدادات إلى ملف"""
        try:
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=4)
        except Exception as e:
            raise Exception(f"خطأ في تصدير الإعدادات: {str(e)}")
    
    def import_settings(self, import_path: str):
        """استيراد الإعدادات من ملف"""
        try:
            if not os.path.exists(import_path):
                raise Exception("ملف الإعدادات غير موجود")
            
            with open(import_path, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            
            if self._validate_settings(imported_settings):
                self.settings = self._merge_settings(self.default_settings, imported_settings)
                self.save_settings()
            else:
                raise Exception("ملف الإعدادات غير صحيح")
                
        except Exception as e:
            raise Exception(f"خطأ في استيراد الإعدادات: {str(e)}")
    
    def get_all_settings(self) -> Dict[str, Any]:
        """الحصول على جميع الإعدادات"""
        return self.settings.copy()
    
    def clear_settings(self):
        """مسح جميع الإعدادات"""
        self.settings = {}
        self.save_settings()
