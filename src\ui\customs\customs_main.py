# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية لنظام إدارة الجمارك
Main Customs Management Interface
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                               QPushButton, QLabel, QFrame, QMessageBox, QGroupBox,
                               QTableWidget, QTableWidgetItem, QLineEdit, QComboBox,
                               QDateEdit, QTextEdit, QFormLayout, QGridLayout, QSpinBox,
                               QDoubleSpinBox, QCheckBox, QInputDialog)
from PySide6.QtCore import Qt, QDate

from ...utils.arabic_support import ArabicSupport
from ...utils.styles import AppStyles


class CustomsMainWidget(QWidget):
    """الواجهة الرئيسية لنظام إدارة الجمارك"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النظام
        title = QLabel("نظام إدارة الجمارك")
        title.setStyleSheet("font-weight: bold; font-size: 18px; color: #2c3e50; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)

        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setLayoutDirection(Qt.RightToLeft)
        self.tab_widget.setStyleSheet(AppStyles.get_tab_style())

        # تبويب البيانات الجمركية
        customs_data_tab = self.create_customs_data_tab()
        self.tab_widget.addTab(customs_data_tab, "البيانات الجمركية")

        # تبويب الرسوم والضرائب
        fees_tab = self.create_fees_tab()
        self.tab_widget.addTab(fees_tab, "الرسوم والضرائب")

        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        self.tab_widget.addTab(reports_tab, "التقارير")

        main_layout.addWidget(self.tab_widget)
        
        # إضافة أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.StyledPanel)
        buttons_frame.setStyleSheet(AppStyles.get_frame_style())
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        
        # الأزرار الأساسية
        self.add_btn = QPushButton("إضافة")
        self.add_btn.setStyleSheet(AppStyles.get_success_button_style())
        self.add_btn.clicked.connect(self.add_record)

        self.edit_btn = QPushButton("تعديل")
        self.edit_btn.setStyleSheet(AppStyles.get_button_style())
        self.edit_btn.clicked.connect(self.edit_record)

        self.save_btn = QPushButton("حفظ")
        self.save_btn.setStyleSheet(AppStyles.get_success_button_style())
        self.save_btn.clicked.connect(self.save_record)

        self.search_btn = QPushButton("بحث")
        self.search_btn.setStyleSheet(AppStyles.get_button_style())
        self.search_btn.clicked.connect(self.search_records)

        self.delete_btn = QPushButton("حذف")
        self.delete_btn.setStyleSheet(AppStyles.get_danger_button_style())
        self.delete_btn.clicked.connect(self.delete_record)

        self.exit_btn = QPushButton("خروج")
        self.exit_btn.setStyleSheet(AppStyles.get_primary_button_style())
        self.exit_btn.clicked.connect(self.close_customs)

        # إضافة الأزرار للتخطيط
        buttons_layout.addWidget(self.add_btn)
        buttons_layout.addWidget(self.edit_btn)
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.search_btn)
        buttons_layout.addWidget(self.delete_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.exit_btn)
        
        layout.addWidget(buttons_frame)

    def create_customs_data_tab(self):
        """إنشاء تبويب البيانات الجمركية"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة بيانات الإدخال الجمركي
        customs_entry_group = QGroupBox("بيانات الإدخال الجمركي")
        customs_entry_group.setStyleSheet(AppStyles.get_frame_style())
        customs_entry_layout = QFormLayout(customs_entry_group)

        self.entry_number = QLineEdit()
        self.entry_number.setPlaceholderText("رقم الإدخال الجمركي...")
        customs_entry_layout.addRow("رقم الإدخال:", self.entry_number)

        self.entry_date = QDateEdit()
        self.entry_date.setDate(QDate.currentDate())
        self.entry_date.setCalendarPopup(True)
        customs_entry_layout.addRow("تاريخ الإدخال:", self.entry_date)

        self.customs_office = QComboBox()
        self.customs_office.addItems(["اختر المكتب الجمركي", "جمارك الملك عبدالعزيز", "جمارك الملك فهد", "جمارك الدمام", "جمارك جدة"])
        customs_entry_layout.addRow("المكتب الجمركي:", self.customs_office)

        self.entry_type = QComboBox()
        self.entry_type.addItems(["استيراد", "تصدير", "ترانزيت", "إعادة تصدير"])
        customs_entry_layout.addRow("نوع الإدخال:", self.entry_type)

        self.shipment_reference = QLineEdit()
        self.shipment_reference.setPlaceholderText("رقم الشحنة المرجعي...")
        customs_entry_layout.addRow("رقم الشحنة:", self.shipment_reference)

        layout.addWidget(customs_entry_group)

        # مجموعة بيانات البضاعة
        goods_data_group = QGroupBox("بيانات البضاعة")
        goods_data_group.setStyleSheet(AppStyles.get_frame_style())
        goods_data_layout = QFormLayout(goods_data_group)

        self.goods_description = QTextEdit()
        self.goods_description.setPlaceholderText("وصف البضاعة...")
        self.goods_description.setMaximumHeight(80)
        goods_data_layout.addRow("وصف البضاعة:", self.goods_description)

        self.hs_code = QLineEdit()
        self.hs_code.setPlaceholderText("رمز النظام المنسق...")
        goods_data_layout.addRow("رمز HS:", self.hs_code)

        self.origin_country = QComboBox()
        self.origin_country.addItems(["اختر البلد", "المملكة العربية السعودية", "الإمارات", "الكويت", "قطر", "البحرين", "عمان", "الصين", "الهند"])
        goods_data_layout.addRow("بلد المنشأ:", self.origin_country)

        self.goods_value = QDoubleSpinBox()
        self.goods_value.setMaximum(999999999.99)
        self.goods_value.setSuffix(" ريال")
        goods_data_layout.addRow("قيمة البضاعة:", self.goods_value)

        self.goods_weight = QDoubleSpinBox()
        self.goods_weight.setMaximum(999999.99)
        self.goods_weight.setSuffix(" كيلو")
        goods_data_layout.addRow("الوزن:", self.goods_weight)

        layout.addWidget(goods_data_group)

        # جدول الإدخالات الجمركية
        customs_entries_group = QGroupBox("قائمة الإدخالات الجمركية")
        customs_entries_group.setStyleSheet(AppStyles.get_frame_style())
        customs_entries_layout = QVBoxLayout(customs_entries_group)

        self.customs_entries_table = QTableWidget()
        self.customs_entries_table.setColumnCount(8)
        self.customs_entries_table.setHorizontalHeaderLabels([
            "رقم الإدخال", "التاريخ", "المكتب الجمركي", "النوع", "وصف البضاعة", "القيمة", "الرسوم", "الحالة"
        ])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.customs_entries_table)

        # إضافة بيانات تجريبية
        self.customs_entries_table.setRowCount(3)
        entries_data = [
            ["CE-001", "2024-01-15", "جمارك الملك عبدالعزيز", "استيراد", "أجهزة كمبيوتر", "15,000.00", "750.00", "مكتمل"],
            ["CE-002", "2024-01-20", "جمارك جدة", "استيراد", "مواد مكتبية", "8,500.00", "425.00", "قيد المراجعة"],
            ["CE-003", "2024-01-25", "جمارك الدمام", "تصدير", "منتجات بلاستيكية", "22,000.00", "0.00", "معلق"]
        ]

        for row, data in enumerate(entries_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 7:  # عمود الحالة
                    if value == "مكتمل":
                        item.setBackground(Qt.green)
                    elif value == "قيد المراجعة":
                        item.setBackground(Qt.yellow)
                    elif value == "معلق":
                        item.setBackground(Qt.red)
                self.customs_entries_table.setItem(row, col, item)

        customs_entries_layout.addWidget(self.customs_entries_table)
        layout.addWidget(customs_entries_group)

        return widget

    def create_fees_tab(self):
        """إنشاء تبويب الرسوم والضرائب"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة حساب الرسوم
        fees_calculation_group = QGroupBox("حساب الرسوم والضرائب")
        fees_calculation_group.setStyleSheet(AppStyles.get_frame_style())
        fees_calculation_layout = QFormLayout(fees_calculation_group)

        self.customs_value = QDoubleSpinBox()
        self.customs_value.setMaximum(999999999.99)
        self.customs_value.setSuffix(" ريال")
        fees_calculation_layout.addRow("القيمة الجمركية:", self.customs_value)

        self.customs_duty_rate = QDoubleSpinBox()
        self.customs_duty_rate.setMaximum(100.0)
        self.customs_duty_rate.setSuffix(" %")
        self.customs_duty_rate.setValue(5.0)
        fees_calculation_layout.addRow("معدل الرسم الجمركي:", self.customs_duty_rate)

        self.vat_rate = QDoubleSpinBox()
        self.vat_rate.setMaximum(100.0)
        self.vat_rate.setSuffix(" %")
        self.vat_rate.setValue(15.0)
        fees_calculation_layout.addRow("معدل ضريبة القيمة المضافة:", self.vat_rate)

        calculate_fees_btn = QPushButton("حساب الرسوم")
        calculate_fees_btn.setStyleSheet(AppStyles.get_button_style())
        fees_calculation_layout.addRow("", calculate_fees_btn)

        layout.addWidget(fees_calculation_group)

        # مجموعة تفاصيل الرسوم
        fees_details_group = QGroupBox("تفاصيل الرسوم المحسوبة")
        fees_details_group.setStyleSheet(AppStyles.get_frame_style())
        fees_details_layout = QFormLayout(fees_details_group)

        self.customs_duty_amount = QLineEdit()
        self.customs_duty_amount.setReadOnly(True)
        self.customs_duty_amount.setText("0.00 ريال")
        fees_details_layout.addRow("الرسم الجمركي:", self.customs_duty_amount)

        self.vat_amount = QLineEdit()
        self.vat_amount.setReadOnly(True)
        self.vat_amount.setText("0.00 ريال")
        fees_details_layout.addRow("ضريبة القيمة المضافة:", self.vat_amount)

        self.total_fees = QLineEdit()
        self.total_fees.setReadOnly(True)
        self.total_fees.setText("0.00 ريال")
        self.total_fees.setStyleSheet("font-weight: bold; background-color: #ecf0f1;")
        fees_details_layout.addRow("إجمالي الرسوم:", self.total_fees)

        layout.addWidget(fees_details_group)

        # جدول الرسوم المدفوعة
        paid_fees_group = QGroupBox("سجل الرسوم المدفوعة")
        paid_fees_group.setStyleSheet(AppStyles.get_frame_style())
        paid_fees_layout = QVBoxLayout(paid_fees_group)

        self.paid_fees_table = QTableWidget()
        self.paid_fees_table.setColumnCount(6)
        self.paid_fees_table.setHorizontalHeaderLabels([
            "رقم الإدخال", "تاريخ الدفع", "الرسم الجمركي", "ضريبة القيمة المضافة", "الإجمالي", "طريقة الدفع"
        ])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.paid_fees_table)

        # إضافة بيانات تجريبية
        self.paid_fees_table.setRowCount(3)
        paid_fees_data = [
            ["CE-001", "2024-01-16", "750.00", "2,250.00", "3,000.00", "تحويل بنكي"],
            ["CE-002", "2024-01-21", "425.00", "1,275.00", "1,700.00", "شيك"],
            ["CE-003", "2024-01-26", "0.00", "0.00", "0.00", "معفى"]
        ]

        for row, data in enumerate(paid_fees_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col in [2, 3, 4]:  # أعمدة المبالغ
                    if value != "0.00":
                        item.setForeground(Qt.darkGreen)
                self.paid_fees_table.setItem(row, col, item)

        paid_fees_layout.addWidget(self.paid_fees_table)
        layout.addWidget(paid_fees_group)

        return widget

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة أنواع التقارير
        reports_types_group = QGroupBox("أنواع التقارير الجمركية")
        reports_types_group.setStyleSheet(AppStyles.get_frame_style())
        reports_types_layout = QGridLayout(reports_types_group)

        # تقرير الإدخالات الشهرية
        monthly_entries_btn = QPushButton("تقرير الإدخالات الشهرية")
        monthly_entries_btn.setStyleSheet(AppStyles.get_button_style())
        reports_types_layout.addWidget(monthly_entries_btn, 0, 0)

        # تقرير الرسوم المحصلة
        collected_fees_btn = QPushButton("تقرير الرسوم المحصلة")
        collected_fees_btn.setStyleSheet(AppStyles.get_success_button_style())
        reports_types_layout.addWidget(collected_fees_btn, 0, 1)

        # تقرير الإدخالات المعلقة
        pending_entries_btn = QPushButton("تقرير الإدخالات المعلقة")
        pending_entries_btn.setStyleSheet(AppStyles.get_danger_button_style())
        reports_types_layout.addWidget(pending_entries_btn, 1, 0)

        # تقرير إحصائيات الجمارك
        customs_stats_btn = QPushButton("تقرير إحصائيات الجمارك")
        customs_stats_btn.setStyleSheet(AppStyles.get_primary_button_style())
        reports_types_layout.addWidget(customs_stats_btn, 1, 1)

        layout.addWidget(reports_types_group)

        # مجموعة إعدادات التقرير
        report_settings_group = QGroupBox("إعدادات التقرير")
        report_settings_group.setStyleSheet(AppStyles.get_frame_style())
        report_settings_layout = QFormLayout(report_settings_group)

        self.report_from_date = QDateEdit()
        self.report_from_date.setDate(QDate.currentDate().addDays(-30))
        self.report_from_date.setCalendarPopup(True)
        report_settings_layout.addRow("من تاريخ:", self.report_from_date)

        self.report_to_date = QDateEdit()
        self.report_to_date.setDate(QDate.currentDate())
        self.report_to_date.setCalendarPopup(True)
        report_settings_layout.addRow("إلى تاريخ:", self.report_to_date)

        self.report_customs_office = QComboBox()
        self.report_customs_office.addItems(["جميع المكاتب", "جمارك الملك عبدالعزيز", "جمارك الملك فهد", "جمارك الدمام", "جمارك جدة"])
        report_settings_layout.addRow("المكتب الجمركي:", self.report_customs_office)

        layout.addWidget(report_settings_group)

        # مجموعة معاينة التقرير
        preview_group = QGroupBox("معاينة التقرير")
        preview_group.setStyleSheet(AppStyles.get_frame_style())
        preview_layout = QVBoxLayout(preview_group)

        self.report_preview_table = QTableWidget()
        self.report_preview_table.setColumnCount(5)
        self.report_preview_table.setHorizontalHeaderLabels([
            "المكتب الجمركي", "عدد الإدخالات", "إجمالي القيمة", "إجمالي الرسوم", "المعدل"
        ])

        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.report_preview_table)

        # إضافة بيانات تجريبية للمعاينة
        self.report_preview_table.setRowCount(4)
        preview_data = [
            ["جمارك الملك عبدالعزيز", "15", "450,000.00", "22,500.00", "5.0%"],
            ["جمارك جدة", "12", "320,000.00", "16,000.00", "5.0%"],
            ["جمارك الدمام", "8", "180,000.00", "9,000.00", "5.0%"],
            ["الإجمالي", "35", "950,000.00", "47,500.00", "5.0%"]
        ]

        for row, data in enumerate(preview_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if row == 3:  # صف الإجمالي
                    item.setStyleSheet("font-weight: bold; background-color: #ecf0f1;")
                self.report_preview_table.setItem(row, col, item)

        preview_layout.addWidget(self.report_preview_table)
        layout.addWidget(preview_group)

        # مجموعة خيارات التصدير
        export_group = QGroupBox("خيارات التصدير")
        export_group.setStyleSheet(AppStyles.get_frame_style())
        export_layout = QHBoxLayout(export_group)

        export_excel_btn = QPushButton("تصدير إلى Excel")
        export_excel_btn.setStyleSheet(AppStyles.get_success_button_style())
        export_layout.addWidget(export_excel_btn)

        export_pdf_btn = QPushButton("تصدير إلى PDF")
        export_pdf_btn.setStyleSheet(AppStyles.get_button_style())
        export_layout.addWidget(export_pdf_btn)

        print_report_btn = QPushButton("طباعة التقرير")
        print_report_btn.setStyleSheet(AppStyles.get_primary_button_style())
        export_layout.addWidget(print_report_btn)

        export_layout.addStretch()
        layout.addWidget(export_group)

        return widget

    def add_record(self):
        """إضافة سجل جديد"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 0:  # تبويب البيانات الجمركية
            self.add_customs_entry()
        elif current_tab == 1:  # تبويب الرسوم والضرائب
            self.add_fee_entry()
        else:
            tab_name = self.tab_widget.tabText(current_tab)
            QMessageBox.information(self, "إضافة", f"إضافة سجل جديد في {tab_name}")

    def edit_record(self):
        """تعديل سجل"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 0:  # تبويب البيانات الجمركية
            self.edit_customs_entry()
        elif current_tab == 1:  # تبويب الرسوم والضرائب
            self.edit_fee_entry()
        else:
            tab_name = self.tab_widget.tabText(current_tab)
            QMessageBox.information(self, "تعديل", f"تعديل سجل في {tab_name}")

    def save_record(self):
        """حفظ السجل"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 0:  # تبويب البيانات الجمركية
            self.save_customs_entry()
        elif current_tab == 1:  # تبويب الرسوم والضرائب
            self.save_fee_entry()
        else:
            tab_name = self.tab_widget.tabText(current_tab)
            QMessageBox.information(self, "حفظ", f"تم حفظ البيانات في {tab_name} بنجاح")

    def search_records(self):
        """البحث في السجلات"""
        current_tab = self.tab_widget.currentIndex()
        tab_name = self.tab_widget.tabText(current_tab)

        # يمكن إضافة نافذة بحث متقدمة هنا
        search_text, ok = QInputDialog.getText(self, "البحث", f"البحث في {tab_name}:")
        if ok and search_text:
            QMessageBox.information(self, "البحث", f"البحث عن: {search_text}")

    def delete_record(self):
        """حذف سجل"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 0:  # تبويب البيانات الجمركية
            self.delete_customs_entry()
        elif current_tab == 1:  # تبويب الرسوم والضرائب
            self.delete_fee_entry()
        else:
            tab_name = self.tab_widget.tabText(current_tab)
            QMessageBox.information(self, "حذف", f"حذف سجل من {tab_name}")

    def add_customs_entry(self):
        """إضافة إدخال جمركي جديد"""
        # إضافة صف جديد للجدول
        row_count = self.customs_entries_table.rowCount()
        self.customs_entries_table.insertRow(row_count)

        # إضافة بيانات افتراضية
        new_entry_id = f"CE-{row_count + 4:03d}"
        current_date = QDate.currentDate().toString("yyyy-MM-dd")

        self.customs_entries_table.setItem(row_count, 0, QTableWidgetItem(new_entry_id))
        self.customs_entries_table.setItem(row_count, 1, QTableWidgetItem(current_date))
        self.customs_entries_table.setItem(row_count, 2, QTableWidgetItem(""))
        self.customs_entries_table.setItem(row_count, 3, QTableWidgetItem("استيراد"))
        self.customs_entries_table.setItem(row_count, 4, QTableWidgetItem(""))
        self.customs_entries_table.setItem(row_count, 5, QTableWidgetItem("0.00"))
        self.customs_entries_table.setItem(row_count, 6, QTableWidgetItem("0.00"))
        self.customs_entries_table.setItem(row_count, 7, QTableWidgetItem("جديد"))

        # تحديد الصف الجديد
        self.customs_entries_table.selectRow(row_count)

        QMessageBox.information(self, "نجح", f"تم إضافة إدخال جمركي جديد: {new_entry_id}")

    def edit_customs_entry(self):
        """تعديل إدخال جمركي"""
        current_row = self.customs_entries_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد إدخال جمركي للتعديل")
            return

        entry_id = self.customs_entries_table.item(current_row, 0).text()
        QMessageBox.information(self, "تعديل", f"يمكنك الآن تعديل الإدخال الجمركي: {entry_id}\nانقر على الخلايا لتعديلها مباشرة")

    def save_customs_entry(self):
        """حفظ الإدخال الجمركي"""
        current_row = self.customs_entries_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد إدخال جمركي للحفظ")
            return

        entry_id = self.customs_entries_table.item(current_row, 0).text()
        QMessageBox.information(self, "نجح", f"تم حفظ الإدخال الجمركي: {entry_id}")

    def delete_customs_entry(self):
        """حذف إدخال جمركي"""
        current_row = self.customs_entries_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد إدخال جمركي للحذف")
            return

        entry_id = self.customs_entries_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل تريد حذف الإدخال الجمركي '{entry_id}'؟\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.customs_entries_table.removeRow(current_row)
            QMessageBox.information(self, "نجح", f"تم حذف الإدخال الجمركي: {entry_id}")

    def add_fee_entry(self):
        """إضافة رسم جديد"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة الرسوم قيد التطوير")

    def edit_fee_entry(self):
        """تعديل رسم"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل الرسوم قيد التطوير")

    def save_fee_entry(self):
        """حفظ رسم"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حفظ الرسوم قيد التطوير")

    def delete_fee_entry(self):
        """حذف رسم"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حذف الرسوم قيد التطوير")

    def close_customs(self):
        """إغلاق نافذة الجمارك"""
        if hasattr(self, 'main_window'):
            # إذا كانت النافذة مفتوحة في وضع ملء الشاشة
            self.window().close()
        else:
            # إذا كانت النافذة مدمجة في النافذة الرئيسية
            self.parent().show_module("welcome")
