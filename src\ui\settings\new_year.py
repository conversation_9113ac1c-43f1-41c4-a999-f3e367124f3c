"""
شاشة السنة الجديدة
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QLineEdit, QDateEdit, QComboBox, QCheckBox,
                               QGroupBox, QPushButton, QFrame, QProgressBar,
                               QMessageBox, QTextEdit, QListWidget)
from PySide6.QtCore import Qt, QDate, QTimer
from PySide6.QtGui import QIcon

from src.ui.styles.app_styles import AppStyles
from src.utils.arabic_support import ArabicSupport


class NewYearWidget(QWidget):
    """شاشة إعداد السنة الجديدة"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الشاشة
        title = QLabel("إعداد السنة المالية الجديدة")
        title.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                font-size: 18px; 
                color: #2c3e50; 
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()
        
        # الجانب الأيسر - إعدادات السنة الجديدة
        self.create_new_year_section(content_layout)
        
        # الجانب الأيمن - معلومات العملية
        self.create_process_section(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # شريط التقدم
        self.create_progress_section(main_layout)
        
        # أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def create_new_year_section(self, layout):
        """إنشاء قسم إعدادات السنة الجديدة"""
        new_year_frame = QFrame()
        new_year_frame.setMaximumWidth(400)
        new_year_layout = QVBoxLayout(new_year_frame)
        
        # مجموعة بيانات السنة الجديدة
        year_group = QGroupBox("بيانات السنة الجديدة")
        year_group.setStyleSheet(AppStyles.get_frame_style())
        year_layout = QFormLayout(year_group)
        
        self.new_year_code = QLineEdit()
        self.new_year_code.setPlaceholderText("رمز السنة الجديدة")
        year_layout.addRow("رمز السنة:", self.new_year_code)
        
        self.new_year_name = QLineEdit()
        self.new_year_name.setPlaceholderText("اسم السنة الجديدة")
        year_layout.addRow("اسم السنة:", self.new_year_name)
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addYears(1))
        self.start_date.setCalendarPopup(True)
        year_layout.addRow("تاريخ البداية:", self.start_date)
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate().addYears(2))
        self.end_date.setCalendarPopup(True)
        year_layout.addRow("تاريخ النهاية:", self.end_date)
        
        new_year_layout.addWidget(year_group)
        
        # مجموعة إعدادات النقل
        transfer_group = QGroupBox("إعدادات النقل")
        transfer_group.setStyleSheet(AppStyles.get_frame_style())
        transfer_layout = QVBoxLayout(transfer_group)
        
        self.transfer_balances = QCheckBox("نقل الأرصدة المدينة والدائنة")
        self.transfer_balances.setChecked(True)
        transfer_layout.addWidget(self.transfer_balances)
        
        self.transfer_items = QCheckBox("نقل بيانات الأصناف")
        self.transfer_items.setChecked(True)
        transfer_layout.addWidget(self.transfer_items)
        
        self.transfer_suppliers = QCheckBox("نقل بيانات الموردين")
        self.transfer_suppliers.setChecked(True)
        transfer_layout.addWidget(self.transfer_suppliers)
        
        self.transfer_settings = QCheckBox("نقل الإعدادات العامة")
        self.transfer_settings.setChecked(True)
        transfer_layout.addWidget(self.transfer_settings)
        
        self.close_old_year = QCheckBox("إغلاق السنة السابقة تلقائياً")
        self.close_old_year.setChecked(True)
        transfer_layout.addWidget(self.close_old_year)
        
        new_year_layout.addWidget(transfer_group)
        
        # مجموعة النسخ الاحتياطي
        backup_group = QGroupBox("النسخ الاحتياطي")
        backup_group.setStyleSheet(AppStyles.get_frame_style())
        backup_layout = QVBoxLayout(backup_group)
        
        self.create_backup = QCheckBox("إنشاء نسخة احتياطية قبل البدء")
        self.create_backup.setChecked(True)
        backup_layout.addWidget(self.create_backup)
        
        self.backup_location = QLineEdit()
        self.backup_location.setPlaceholderText("مسار النسخة الاحتياطية")
        self.backup_location.setText("C:/Backups/")
        backup_layout.addWidget(self.backup_location)
        
        browse_btn = QPushButton("تصفح...")
        browse_btn.clicked.connect(self.browse_backup_location)
        backup_layout.addWidget(browse_btn)
        
        new_year_layout.addWidget(backup_group)
        new_year_layout.addStretch()
        
        layout.addWidget(new_year_frame)
    
    def create_process_section(self, layout):
        """إنشاء قسم معلومات العملية"""
        process_frame = QFrame()
        process_layout = QVBoxLayout(process_frame)
        
        # معلومات السنة الحالية
        current_year_group = QGroupBox("السنة المالية الحالية")
        current_year_group.setStyleSheet(AppStyles.get_frame_style())
        current_year_layout = QFormLayout(current_year_group)
        
        current_year_layout.addRow("رمز السنة:", QLabel("FY2024"))
        current_year_layout.addRow("اسم السنة:", QLabel("السنة المالية 2024"))
        current_year_layout.addRow("تاريخ البداية:", QLabel("2024-01-01"))
        current_year_layout.addRow("تاريخ النهاية:", QLabel("2024-12-31"))
        current_year_layout.addRow("الحالة:", QLabel("نشطة"))
        
        process_layout.addWidget(current_year_group)
        
        # قائمة العمليات
        operations_group = QGroupBox("العمليات المطلوبة")
        operations_group.setStyleSheet(AppStyles.get_frame_style())
        operations_layout = QVBoxLayout(operations_group)
        
        self.operations_list = QListWidget()
        operations = [
            "إنشاء نسخة احتياطية من البيانات",
            "إنشاء السنة المالية الجديدة",
            "نقل بيانات الأصناف",
            "نقل بيانات الموردين",
            "نقل الإعدادات العامة",
            "نقل الأرصدة الافتتاحية",
            "إغلاق السنة السابقة",
            "تفعيل السنة الجديدة"
        ]
        
        for operation in operations:
            self.operations_list.addItem(f"⏳ {operation}")
        
        operations_layout.addWidget(self.operations_list)
        process_layout.addWidget(operations_group)
        
        # سجل العمليات
        log_group = QGroupBox("سجل العمليات")
        log_group.setStyleSheet(AppStyles.get_frame_style())
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        self.log_text.append("جاهز لبدء عملية إنشاء السنة الجديدة...")
        log_layout.addWidget(self.log_text)
        
        process_layout.addWidget(log_group)
        layout.addWidget(process_frame)
    
    def create_progress_section(self, layout):
        """إنشاء قسم شريط التقدم"""
        progress_frame = QFrame()
        progress_layout = QVBoxLayout(progress_frame)
        
        progress_label = QLabel("تقدم العملية:")
        progress_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        progress_layout.addWidget(progress_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #27ae60;
                border-radius: 3px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)
        
        layout.addWidget(progress_frame)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر بدء العملية
        self.start_btn = QPushButton("بدء إنشاء السنة الجديدة")
        self.start_btn.setIcon(QIcon("assets/icons/play.png"))
        self.start_btn.clicked.connect(self.start_new_year_process)
        self.start_btn.setStyleSheet(AppStyles.get_button_style("success"))
        buttons_layout.addWidget(self.start_btn)
        
        # زر إيقاف العملية
        self.stop_btn = QPushButton("إيقاف العملية")
        self.stop_btn.setIcon(QIcon("assets/icons/stop.png"))
        self.stop_btn.clicked.connect(self.stop_process)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet(AppStyles.get_button_style("danger"))
        buttons_layout.addWidget(self.stop_btn)
        
        # زر معاينة التقرير
        preview_btn = QPushButton("معاينة التقرير")
        preview_btn.setIcon(QIcon("assets/icons/preview.png"))
        preview_btn.clicked.connect(self.preview_report)
        preview_btn.setStyleSheet(AppStyles.get_button_style("info"))
        buttons_layout.addWidget(preview_btn)
        
        buttons_layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setIcon(QIcon("assets/icons/close.png"))
        close_btn.clicked.connect(self.close_screen)
        close_btn.setStyleSheet(AppStyles.get_button_style("secondary"))
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)
    
    def browse_backup_location(self):
        """تصفح مسار النسخة الاحتياطية"""
        from PySide6.QtWidgets import QFileDialog
        folder = QFileDialog.getExistingDirectory(self, "اختيار مجلد النسخة الاحتياطية")
        if folder:
            self.backup_location.setText(folder)
    
    def start_new_year_process(self):
        """بدء عملية إنشاء السنة الجديدة"""
        if not self.new_year_code.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز السنة الجديدة")
            return
        
        reply = QMessageBox.question(self, "تأكيد العملية", 
                                   "هل تريد بدء عملية إنشاء السنة المالية الجديدة؟\n"
                                   "هذه العملية قد تستغرق بعض الوقت.")
        
        if reply == QMessageBox.Yes:
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.simulate_process()
    
    def simulate_process(self):
        """محاكاة عملية إنشاء السنة الجديدة"""
        self.current_step = 0
        self.total_steps = 8
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_progress)
        self.timer.start(2000)  # كل ثانيتين
    
    def update_progress(self):
        """تحديث شريط التقدم"""
        if self.current_step < self.total_steps:
            progress = int((self.current_step + 1) / self.total_steps * 100)
            self.progress_bar.setValue(progress)
            
            # تحديث قائمة العمليات
            item = self.operations_list.item(self.current_step)
            if item:
                text = item.text().replace("⏳", "✅")
                item.setText(text)
            
            # تحديث السجل
            operations = [
                "تم إنشاء نسخة احتياطية بنجاح",
                "تم إنشاء السنة المالية الجديدة",
                "تم نقل بيانات الأصناف",
                "تم نقل بيانات الموردين",
                "تم نقل الإعدادات العامة",
                "تم نقل الأرصدة الافتتاحية",
                "تم إغلاق السنة السابقة",
                "تم تفعيل السنة الجديدة بنجاح"
            ]
            
            self.log_text.append(f"✅ {operations[self.current_step]}")
            
            self.current_step += 1
        else:
            self.timer.stop()
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            QMessageBox.information(self, "اكتملت العملية", 
                                  "تم إنشاء السنة المالية الجديدة بنجاح!")
    
    def stop_process(self):
        """إيقاف العملية"""
        if hasattr(self, 'timer'):
            self.timer.stop()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.log_text.append("⚠️ تم إيقاف العملية بواسطة المستخدم")
    
    def preview_report(self):
        """معاينة التقرير"""
        QMessageBox.information(self, "معاينة التقرير", 
                              "سيتم عرض تقرير مفصل عن عملية إنشاء السنة الجديدة...")
    
    def close_screen(self):
        """إغلاق الشاشة"""
        if hasattr(self, 'main_window'):
            self.window().close()
        else:
            self.parent().close()
