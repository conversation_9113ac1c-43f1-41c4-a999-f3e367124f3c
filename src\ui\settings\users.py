"""
شاشة إدارة المستخدمين
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLabel, QLineEdit, QComboBox, QCheckBox,
                               QGroupBox, QPushButton, QFrame,
                               QMessageBox, QTableWidget, QTableWidgetItem)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from src.utils.styles import AppStyles
from src.utils.arabic_support import ArabicSupport


class UsersWidget(QWidget):
    """شاشة إدارة المستخدمين"""
    
    def __init__(self):
        super().__init__()
        self.arabic_support = ArabicSupport()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الشاشة
        title = QLabel("إدارة المستخدمين والصلاحيات")
        title.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                font-size: 18px; 
                color: #2c3e50; 
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 2px solid #bdc3c7;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()
        
        # الجانب الأيسر - نموذج البيانات
        self.create_form_section(content_layout)
        
        # الجانب الأيمن - جدول المستخدمين
        self.create_table_section(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # أزرار التحكم
        self.add_control_buttons(main_layout)
    
    def create_form_section(self, layout):
        """إنشاء قسم النموذج"""
        form_frame = QFrame()
        form_frame.setMaximumWidth(400)
        form_layout = QVBoxLayout(form_frame)
        
        # مجموعة بيانات المستخدم
        user_group = QGroupBox("بيانات المستخدم")
        user_group.setStyleSheet(AppStyles.get_frame_style())
        user_layout = QFormLayout(user_group)
        
        self.username = QLineEdit()
        self.username.setPlaceholderText("اسم المستخدم")
        user_layout.addRow("اسم المستخدم:", self.username)
        
        self.full_name = QLineEdit()
        self.full_name.setPlaceholderText("الاسم الكامل")
        user_layout.addRow("الاسم الكامل:", self.full_name)
        
        self.email = QLineEdit()
        self.email.setPlaceholderText("البريد الإلكتروني")
        user_layout.addRow("البريد الإلكتروني:", self.email)
        
        self.phone = QLineEdit()
        self.phone.setPlaceholderText("رقم الهاتف")
        user_layout.addRow("رقم الهاتف:", self.phone)
        
        self.password = QLineEdit()
        self.password.setEchoMode(QLineEdit.Password)
        self.password.setPlaceholderText("كلمة المرور")
        user_layout.addRow("كلمة المرور:", self.password)
        
        self.confirm_password = QLineEdit()
        self.confirm_password.setEchoMode(QLineEdit.Password)
        self.confirm_password.setPlaceholderText("تأكيد كلمة المرور")
        user_layout.addRow("تأكيد كلمة المرور:", self.confirm_password)
        
        form_layout.addWidget(user_group)
        
        # مجموعة الصلاحيات
        permissions_group = QGroupBox("الصلاحيات")
        permissions_group.setStyleSheet(AppStyles.get_frame_style())
        permissions_layout = QFormLayout(permissions_group)
        
        self.user_role = QComboBox()
        self.user_role.addItems(["مدير النظام", "مدير", "مستخدم", "مستخدم محدود"])
        permissions_layout.addRow("الدور:", self.user_role)
        
        self.is_active = QCheckBox("مستخدم نشط")
        self.is_active.setChecked(True)
        permissions_layout.addRow("الحالة:", self.is_active)
        
        self.can_add = QCheckBox("إضافة")
        permissions_layout.addRow("صلاحية الإضافة:", self.can_add)
        
        self.can_edit = QCheckBox("تعديل")
        permissions_layout.addRow("صلاحية التعديل:", self.can_edit)
        
        self.can_delete = QCheckBox("حذف")
        permissions_layout.addRow("صلاحية الحذف:", self.can_delete)
        
        self.can_view_reports = QCheckBox("عرض التقارير")
        permissions_layout.addRow("صلاحية التقارير:", self.can_view_reports)
        
        form_layout.addWidget(permissions_group)
        form_layout.addStretch()
        
        layout.addWidget(form_frame)
    
    def create_table_section(self, layout):
        """إنشاء قسم الجدول"""
        table_frame = QFrame()
        table_layout = QVBoxLayout(table_frame)
        
        # عنوان الجدول
        table_title = QLabel("المستخدمين المسجلين")
        table_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50; padding: 10px;")
        table_layout.addWidget(table_title)
        
        # الجدول
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(6)
        self.users_table.setHorizontalHeaderLabels([
            "اسم المستخدم", "الاسم الكامل", "البريد الإلكتروني", "الدور", "الحالة", "آخر دخول"
        ])
        
        # تطبيق الأنماط والإعدادات المحسنة
        AppStyles.setup_table_widget(self.users_table)
        
        # إضافة بيانات تجريبية
        self.users_table.setRowCount(4)
        users_data = [
            ["admin", "مدير النظام", "<EMAIL>", "مدير النظام", "نشط", "2024-01-15 10:30"],
            ["manager1", "أحمد محمد", "<EMAIL>", "مدير", "نشط", "2024-01-15 09:15"],
            ["user1", "فاطمة علي", "<EMAIL>", "مستخدم", "نشط", "2024-01-14 16:45"],
            ["user2", "محمد سالم", "<EMAIL>", "مستخدم", "غير نشط", "2024-01-10 14:20"]
        ]
        
        for row, data in enumerate(users_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 3:  # عمود الدور
                    if value == "مدير النظام":
                        item.setBackground(AppStyles.get_color("danger_light"))
                    elif value == "مدير":
                        item.setBackground(AppStyles.get_color("warning_light"))
                    else:
                        item.setBackground(AppStyles.get_color("info_light"))
                elif col == 4:  # عمود الحالة
                    if value == "نشط":
                        item.setBackground(AppStyles.get_color("success_light"))
                    else:
                        item.setBackground(AppStyles.get_color("secondary_light"))
                
                self.users_table.setItem(row, col, item)
        
        table_layout.addWidget(self.users_table)
        layout.addWidget(table_frame)
    
    def add_control_buttons(self, layout):
        """إضافة أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر إضافة
        add_btn = QPushButton("إضافة مستخدم")
        add_btn.setIcon(QIcon("assets/icons/add.png"))
        add_btn.clicked.connect(self.add_user)
        add_btn.setStyleSheet(AppStyles.get_button_style("primary"))
        buttons_layout.addWidget(add_btn)
        
        # زر تعديل
        edit_btn = QPushButton("تعديل")
        edit_btn.setIcon(QIcon("assets/icons/edit.png"))
        edit_btn.clicked.connect(self.edit_user)
        edit_btn.setStyleSheet(AppStyles.get_button_style("info"))
        buttons_layout.addWidget(edit_btn)
        
        # زر حفظ
        save_btn = QPushButton("حفظ")
        save_btn.setIcon(QIcon("assets/icons/save.png"))
        save_btn.clicked.connect(self.save_user)
        save_btn.setStyleSheet(AppStyles.get_button_style("success"))
        buttons_layout.addWidget(save_btn)
        
        # زر حذف
        delete_btn = QPushButton("حذف")
        delete_btn.setIcon(QIcon("assets/icons/delete.png"))
        delete_btn.clicked.connect(self.delete_user)
        delete_btn.setStyleSheet(AppStyles.get_button_style("danger"))
        buttons_layout.addWidget(delete_btn)
        
        # زر إعادة تعيين كلمة المرور
        reset_password_btn = QPushButton("إعادة تعيين كلمة المرور")
        reset_password_btn.setIcon(QIcon("assets/icons/key.png"))
        reset_password_btn.clicked.connect(self.reset_password)
        reset_password_btn.setStyleSheet(AppStyles.get_button_style("warning"))
        buttons_layout.addWidget(reset_password_btn)
        
        buttons_layout.addStretch()
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setIcon(QIcon("assets/icons/close.png"))
        close_btn.clicked.connect(self.close_screen)
        close_btn.setStyleSheet(AppStyles.get_button_style("secondary"))
        buttons_layout.addWidget(close_btn)
        
        layout.addWidget(buttons_frame)
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        if not self.username.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المستخدم")
            return
        
        if self.password.text() != self.confirm_password.text():
            QMessageBox.warning(self, "تحذير", "كلمة المرور وتأكيدها غير متطابقتين")
            return
        
        QMessageBox.information(self, "نجح", "تم إضافة المستخدم بنجاح!")
        self.clear_form()
    
    def edit_user(self):
        """تعديل مستخدم"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم للتعديل")
            return
        
        self.load_user_data(current_row)
    
    def save_user(self):
        """حفظ المستخدم"""
        try:
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ بيانات المستخدم بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ البيانات: {str(e)}")
    
    def delete_user(self):
        """حذف مستخدم"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم للحذف")
            return
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   "هل تريد حذف المستخدم المختار؟")
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم الحذف", "تم حذف المستخدم بنجاح!")
    
    def reset_password(self):
        """إعادة تعيين كلمة المرور"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم لإعادة تعيين كلمة المرور")
            return
        
        QMessageBox.information(self, "إعادة تعيين", "تم إعادة تعيين كلمة المرور بنجاح!")
    
    def load_user_data(self, row):
        """تحميل بيانات المستخدم"""
        self.username.setText(self.users_table.item(row, 0).text())
        self.full_name.setText(self.users_table.item(row, 1).text())
        self.email.setText(self.users_table.item(row, 2).text())
    
    def clear_form(self):
        """مسح النموذج"""
        self.username.clear()
        self.full_name.clear()
        self.email.clear()
        self.phone.clear()
        self.password.clear()
        self.confirm_password.clear()
        self.is_active.setChecked(True)
        self.can_add.setChecked(False)
        self.can_edit.setChecked(False)
        self.can_delete.setChecked(False)
        self.can_view_reports.setChecked(False)
    
    def close_screen(self):
        """إغلاق الشاشة"""
        if hasattr(self, 'main_window'):
            self.window().close()
        else:
            self.parent().close()
